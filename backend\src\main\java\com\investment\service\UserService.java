package com.investment.service;

import com.investment.dto.LoginRequest;
import com.investment.dto.RegisterRequest;
import com.investment.dto.Result;
import com.investment.entity.User;

/**
 * 用户服务接口
 */
public interface UserService {
    
    /**
     * 用户注册
     * @param registerRequest 注册请求
     * @return 注册结果
     */
    Result<String> register(RegisterRequest registerRequest);
    
    /**
     * 用户登录
     * @param loginRequest 登录请求
     * @return 登录结果，包含JWT token
     */
    Result<String> login(LoginRequest loginRequest);
    
    /**
     * 根据用户名查询用户
     * @param username 用户名
     * @return 用户信息
     */
    User findByUsername(String username);
    
    /**
     * 根据用户ID查询用户
     * @param id 用户ID
     * @return 用户信息
     */
    User findById(Long id);
    
    /**
     * 验证密码
     * @param rawPassword 原始密码
     * @param encodedPassword 加密后的密码
     * @return 验证结果
     */
    boolean verifyPassword(String rawPassword, String encodedPassword);
    
    /**
     * 加密密码
     * @param password 原始密码
     * @return 加密后的密码
     */
    String encodePassword(String password);
}
