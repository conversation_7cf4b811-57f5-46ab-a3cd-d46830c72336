[{"E:\\java毕设\\胜利投资理财网站\\frontend\\src\\main.js": "1", "E:\\java毕设\\胜利投资理财网站\\frontend\\src\\App.vue": "2", "E:\\java毕设\\胜利投资理财网站\\frontend\\src\\router\\index.js": "3", "E:\\java毕设\\胜利投资理财网站\\frontend\\src\\views\\Dashboard.vue": "4", "E:\\java毕设\\胜利投资理财网站\\frontend\\src\\views\\Login.vue": "5", "E:\\java毕设\\胜利投资理财网站\\frontend\\src\\views\\Register.vue": "6", "E:\\java毕设\\胜利投资理财网站\\frontend\\src\\api\\user.js": "7", "E:\\java毕设\\胜利投资理财网站\\frontend\\src\\api\\request.js": "8"}, {"size": 309, "mtime": 1754411325321, "results": "9", "hashOfConfig": "10"}, {"size": 395, "mtime": 1754411332019, "results": "11", "hashOfConfig": "10"}, {"size": 1497, "mtime": 1754411429550, "results": "12", "hashOfConfig": "10"}, {"size": 3475, "mtime": 1754411449015, "results": "13", "hashOfConfig": "10"}, {"size": 3076, "mtime": 1754411364678, "results": "14", "hashOfConfig": "10"}, {"size": 4980, "mtime": 1754411392413, "results": "15", "hashOfConfig": "10"}, {"size": 808, "mtime": 1754411418844, "results": "16", "hashOfConfig": "10"}, {"size": 2116, "mtime": 1754411411462, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "l7wbag", {"filePath": "20", "messages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": "24"}, {"filePath": "25", "messages": "26", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 5, "fixableWarningCount": 0, "source": "27"}, {"filePath": "28", "messages": "29", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 6, "fixableWarningCount": 0, "source": "30"}, {"filePath": "31", "messages": "32", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 9, "fixableWarningCount": 0, "source": "33"}, {"filePath": "34", "messages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 7, "fixableWarningCount": 0, "source": "38"}, "E:\\java毕设\\胜利投资理财网站\\frontend\\src\\main.js", [], "E:\\java毕设\\胜利投资理财网站\\frontend\\src\\App.vue", [], "E:\\java毕设\\胜利投资理财网站\\frontend\\src\\router\\index.js", ["39"], "import Vue from 'vue'\nimport VueRouter from 'vue-router'\nimport Cookies from 'js-cookie'\n\nVue.use(VueRouter)\n\nconst routes = [\n  {\n    path: '/',\n    redirect: '/login'\n  },\n  {\n    path: '/login',\n    name: 'Login',\n    component: () => import('@/views/Login.vue'),\n    meta: {\n      title: '用户登录',\n      requiresAuth: false\n    }\n  },\n  {\n    path: '/register',\n    name: 'Register',\n    component: () => import('@/views/Register.vue'),\n    meta: {\n      title: '用户注册',\n      requiresAuth: false\n    }\n  },\n  {\n    path: '/dashboard',\n    name: 'Dashboard',\n    component: () => import('@/views/Dashboard.vue'),\n    meta: {\n      title: '控制台',\n      requiresAuth: true\n    }\n  }\n]\n\nconst router = new VueRouter({\n  mode: 'history',\n  base: process.env.BASE_URL,\n  routes\n})\n\n// 路由守卫\nrouter.beforeEach((to, from, next) => {\n  // 设置页面标题\n  document.title = to.meta.title || '胜利投资理财网站'\n  \n  // 检查是否需要登录\n  if (to.meta.requiresAuth) {\n    const token = Cookies.get('token')\n    if (!token) {\n      // 未登录，跳转到登录页\n      next({\n        path: '/login',\n        query: { redirect: to.fullPath }\n      })\n    } else {\n      next()\n    }\n  } else {\n    // 如果已登录且访问登录/注册页，跳转到控制台\n    const token = Cookies.get('token')\n    if (token && (to.path === '/login' || to.path === '/register')) {\n      next('/dashboard')\n    } else {\n      next()\n    }\n  }\n})\n\nexport default router\n", "E:\\java毕设\\胜利投资理财网站\\frontend\\src\\views\\Dashboard.vue", ["40", "41", "42", "43", "44"], "<template>\n  <div class=\"dashboard\">\n    <el-container>\n      <!-- 头部 -->\n      <el-header class=\"header\">\n        <div class=\"header-left\">\n          <h2>胜利投资理财网站</h2>\n        </div>\n        <div class=\"header-right\">\n          <el-dropdown @command=\"handleCommand\">\n            <span class=\"el-dropdown-link\">\n              欢迎您 <i class=\"el-icon-arrow-down el-icon--right\"></i>\n            </span>\n            <el-dropdown-menu slot=\"dropdown\">\n              <el-dropdown-item command=\"profile\">个人中心</el-dropdown-item>\n              <el-dropdown-item command=\"logout\">退出登录</el-dropdown-item>\n            </el-dropdown-menu>\n          </el-dropdown>\n        </div>\n      </el-header>\n      \n      <!-- 主体内容 -->\n      <el-main class=\"main\">\n        <div class=\"welcome-card\">\n          <el-card>\n            <h3>欢迎来到胜利投资理财网站</h3>\n            <p>您已成功登录系统！</p>\n            <p>这里是用户控制台，后续可以添加更多功能模块。</p>\n            \n            <div class=\"test-section\">\n              <h4>测试后端连接：</h4>\n              <el-button type=\"primary\" @click=\"testBackend\" :loading=\"testing\">\n                测试后端接口\n              </el-button>\n              <p v-if=\"testResult\" :class=\"testResult.success ? 'success' : 'error'\">\n                {{ testResult.message }}\n              </p>\n            </div>\n          </el-card>\n        </div>\n      </el-main>\n    </el-container>\n  </div>\n</template>\n\n<script>\nimport { test } from '@/api/user'\nimport Cookies from 'js-cookie'\n\nexport default {\n  name: 'Dashboard',\n  data() {\n    return {\n      testing: false,\n      testResult: null\n    }\n  },\n  methods: {\n    handleCommand(command) {\n      switch (command) {\n        case 'profile':\n          this.$message.info('个人中心功能待开发')\n          break\n        case 'logout':\n          this.logout()\n          break\n      }\n    },\n    \n    logout() {\n      this.$confirm('确定要退出登录吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        Cookies.remove('token')\n        this.$message.success('退出登录成功')\n        this.$router.push('/login')\n      })\n    },\n    \n    async testBackend() {\n      this.testing = true\n      this.testResult = null\n      \n      try {\n        const response = await test()\n        this.testResult = {\n          success: true,\n          message: response.data || '后端连接成功'\n        }\n      } catch (error) {\n        this.testResult = {\n          success: false,\n          message: '后端连接失败：' + error.message\n        }\n      } finally {\n        this.testing = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.dashboard {\n  min-height: 100vh;\n}\n\n.header {\n  background-color: #409eff;\n  color: white;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 20px;\n}\n\n.header-left h2 {\n  margin: 0;\n}\n\n.header-right {\n  cursor: pointer;\n}\n\n.el-dropdown-link {\n  color: white;\n}\n\n.main {\n  padding: 20px;\n  background-color: #f5f5f5;\n}\n\n.welcome-card {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.test-section {\n  margin-top: 30px;\n  padding-top: 20px;\n  border-top: 1px solid #eee;\n}\n\n.test-section h4 {\n  margin-bottom: 15px;\n}\n\n.success {\n  color: #67c23a;\n  margin-top: 10px;\n}\n\n.error {\n  color: #f56c6c;\n  margin-top: 10px;\n}\n</style>\n", "E:\\java毕设\\胜利投资理财网站\\frontend\\src\\views\\Login.vue", ["45", "46", "47", "48", "49", "50"], "<template>\n  <div class=\"auth-container\">\n    <div class=\"auth-card\">\n      <h2 class=\"auth-title\">用户登录</h2>\n      <el-form\n        ref=\"loginForm\"\n        :model=\"loginForm\"\n        :rules=\"loginRules\"\n        class=\"auth-form\"\n        @submit.native.prevent=\"handleLogin\"\n      >\n        <el-form-item prop=\"username\">\n          <el-input\n            v-model=\"loginForm.username\"\n            placeholder=\"请输入用户名\"\n            prefix-icon=\"el-icon-user\"\n            clearable\n          />\n        </el-form-item>\n        \n        <el-form-item prop=\"password\">\n          <el-input\n            v-model=\"loginForm.password\"\n            type=\"password\"\n            placeholder=\"请输入密码\"\n            prefix-icon=\"el-icon-lock\"\n            show-password\n            clearable\n            @keyup.enter.native=\"handleLogin\"\n          />\n        </el-form-item>\n        \n        <el-form-item>\n          <el-button\n            type=\"primary\"\n            class=\"auth-button\"\n            :loading=\"loading\"\n            @click=\"handleLogin\"\n          >\n            {{ loading ? '登录中...' : '登录' }}\n          </el-button>\n        </el-form-item>\n      </el-form>\n      \n      <div class=\"auth-link\">\n        <span>还没有账号？</span>\n        <router-link to=\"/register\">立即注册</router-link>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { login } from '@/api/user'\nimport Cookies from 'js-cookie'\n\nexport default {\n  name: 'Login',\n  data() {\n    return {\n      loginForm: {\n        username: '',\n        password: ''\n      },\n      loginRules: {\n        username: [\n          { required: true, message: '请输入用户名', trigger: 'blur' },\n          { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }\n        ],\n        password: [\n          { required: true, message: '请输入密码', trigger: 'blur' },\n          { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }\n        ]\n      },\n      loading: false\n    }\n  },\n  methods: {\n    handleLogin() {\n      this.$refs.loginForm.validate(async (valid) => {\n        if (!valid) {\n          return false\n        }\n        \n        this.loading = true\n        try {\n          const response = await login(this.loginForm)\n          if (response.code === 200) {\n            // 保存token\n            Cookies.set('token', response.data, { expires: 1 })\n            \n            this.$message.success('登录成功')\n            \n            // 跳转到首页或之前访问的页面\n            const redirect = this.$route.query.redirect || '/dashboard'\n            this.$router.push(redirect)\n          } else {\n            this.$message.error(response.message || '登录失败')\n          }\n        } catch (error) {\n          console.error('登录错误：', error)\n          this.$message.error('登录失败，请检查网络连接')\n        } finally {\n          this.loading = false\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* 组件特定样式可以在这里添加 */\n</style>\n", "E:\\java毕设\\胜利投资理财网站\\frontend\\src\\views\\Register.vue", ["51", "52", "53", "54", "55", "56", "57", "58", "59"], "<template>\n  <div class=\"auth-container\">\n    <div class=\"auth-card\">\n      <h2 class=\"auth-title\">用户注册</h2>\n      <el-form\n        ref=\"registerForm\"\n        :model=\"registerForm\"\n        :rules=\"registerRules\"\n        class=\"auth-form\"\n        @submit.native.prevent=\"handleRegister\"\n      >\n        <el-form-item prop=\"username\">\n          <el-input\n            v-model=\"registerForm.username\"\n            placeholder=\"请输入用户名\"\n            prefix-icon=\"el-icon-user\"\n            clearable\n          />\n        </el-form-item>\n        \n        <el-form-item prop=\"email\">\n          <el-input\n            v-model=\"registerForm.email\"\n            placeholder=\"请输入邮箱\"\n            prefix-icon=\"el-icon-message\"\n            clearable\n          />\n        </el-form-item>\n        \n        <el-form-item prop=\"phone\">\n          <el-input\n            v-model=\"registerForm.phone\"\n            placeholder=\"请输入手机号\"\n            prefix-icon=\"el-icon-phone\"\n            clearable\n          />\n        </el-form-item>\n        \n        <el-form-item prop=\"realName\">\n          <el-input\n            v-model=\"registerForm.realName\"\n            placeholder=\"请输入真实姓名（可选）\"\n            prefix-icon=\"el-icon-user-solid\"\n            clearable\n          />\n        </el-form-item>\n        \n        <el-form-item prop=\"password\">\n          <el-input\n            v-model=\"registerForm.password\"\n            type=\"password\"\n            placeholder=\"请输入密码\"\n            prefix-icon=\"el-icon-lock\"\n            show-password\n            clearable\n          />\n        </el-form-item>\n        \n        <el-form-item prop=\"confirmPassword\">\n          <el-input\n            v-model=\"registerForm.confirmPassword\"\n            type=\"password\"\n            placeholder=\"请确认密码\"\n            prefix-icon=\"el-icon-lock\"\n            show-password\n            clearable\n            @keyup.enter.native=\"handleRegister\"\n          />\n        </el-form-item>\n        \n        <el-form-item>\n          <el-button\n            type=\"primary\"\n            class=\"auth-button\"\n            :loading=\"loading\"\n            @click=\"handleRegister\"\n          >\n            {{ loading ? '注册中...' : '注册' }}\n          </el-button>\n        </el-form-item>\n      </el-form>\n      \n      <div class=\"auth-link\">\n        <span>已有账号？</span>\n        <router-link to=\"/login\">立即登录</router-link>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { register } from '@/api/user'\n\nexport default {\n  name: 'Register',\n  data() {\n    // 自定义验证规则\n    const validateConfirmPassword = (rule, value, callback) => {\n      if (value !== this.registerForm.password) {\n        callback(new Error('两次输入的密码不一致'))\n      } else {\n        callback()\n      }\n    }\n    \n    return {\n      registerForm: {\n        username: '',\n        email: '',\n        phone: '',\n        realName: '',\n        password: '',\n        confirmPassword: ''\n      },\n      registerRules: {\n        username: [\n          { required: true, message: '请输入用户名', trigger: 'blur' },\n          { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },\n          { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }\n        ],\n        email: [\n          { required: true, message: '请输入邮箱', trigger: 'blur' },\n          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }\n        ],\n        phone: [\n          { required: true, message: '请输入手机号', trigger: 'blur' },\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }\n        ],\n        password: [\n          { required: true, message: '请输入密码', trigger: 'blur' },\n          { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }\n        ],\n        confirmPassword: [\n          { required: true, message: '请确认密码', trigger: 'blur' },\n          { validator: validateConfirmPassword, trigger: 'blur' }\n        ]\n      },\n      loading: false\n    }\n  },\n  methods: {\n    handleRegister() {\n      this.$refs.registerForm.validate(async (valid) => {\n        if (!valid) {\n          return false\n        }\n        \n        this.loading = true\n        try {\n          const response = await register(this.registerForm)\n          if (response.code === 200) {\n            this.$message.success('注册成功，请登录')\n            this.$router.push('/login')\n          } else {\n            this.$message.error(response.message || '注册失败')\n          }\n        } catch (error) {\n          console.error('注册错误：', error)\n          this.$message.error('注册失败，请检查网络连接')\n        } finally {\n          this.loading = false\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.auth-card {\n  width: 450px;\n}\n</style>\n", "E:\\java毕设\\胜利投资理财网站\\frontend\\src\\api\\user.js", [], "E:\\java毕设\\胜利投资理财网站\\frontend\\src\\api\\request.js", ["60", "61", "62", "63", "64", "65", "66"], "import axios from 'axios'\nimport { Message } from 'element-ui'\nimport Cookies from 'js-cookie'\n\n// 创建axios实例\nconst service = axios.create({\n  baseURL: process.env.NODE_ENV === 'production' ? '/api' : '/api',\n  timeout: 10000\n})\n\n// 请求拦截器\nservice.interceptors.request.use(\n  config => {\n    // 添加token到请求头\n    const token = Cookies.get('token')\n    if (token) {\n      config.headers['Authorization'] = `Bearer ${token}`\n    }\n    return config\n  },\n  error => {\n    console.error('请求错误：', error)\n    return Promise.reject(error)\n  }\n)\n\n// 响应拦截器\nservice.interceptors.response.use(\n  response => {\n    const res = response.data\n    \n    // 如果返回的状态码不是200，则显示错误信息\n    if (res.code !== 200) {\n      Message({\n        message: res.message || '请求失败',\n        type: 'error',\n        duration: 3000\n      })\n      \n      // 401表示未授权，跳转到登录页\n      if (res.code === 401) {\n        Cookies.remove('token')\n        window.location.href = '/login'\n      }\n      \n      return Promise.reject(new Error(res.message || '请求失败'))\n    } else {\n      return res\n    }\n  },\n  error => {\n    console.error('响应错误：', error)\n    \n    let message = '网络错误'\n    if (error.response) {\n      switch (error.response.status) {\n        case 400:\n          message = '请求参数错误'\n          break\n        case 401:\n          message = '未授权，请重新登录'\n          Cookies.remove('token')\n          window.location.href = '/login'\n          break\n        case 403:\n          message = '拒绝访问'\n          break\n        case 404:\n          message = '请求地址不存在'\n          break\n        case 500:\n          message = '服务器内部错误'\n          break\n        default:\n          message = `连接错误${error.response.status}`\n      }\n    } else if (error.code === 'ECONNABORTED') {\n      message = '请求超时'\n    }\n    \n    Message({\n      message,\n      type: 'error',\n      duration: 3000\n    })\n    \n    return Promise.reject(error)\n  }\n)\n\nexport default service\n", {"ruleId": "67", "severity": 2, "message": "68", "line": 51, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 51, "endColumn": 3, "fix": "71"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 21, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 21, "endColumn": 7, "fix": "72"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 29, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 29, "endColumn": 13, "fix": "73"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 69, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 69, "endColumn": 5, "fix": "74"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 81, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 81, "endColumn": 5, "fix": "75"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 85, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 85, "endColumn": 7, "fix": "76"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 20, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 20, "endColumn": 9, "fix": "77"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 32, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 32, "endColumn": 9, "fix": "78"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 44, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 44, "endColumn": 7, "fix": "79"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 84, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 84, "endColumn": 9, "fix": "80"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 91, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 91, "endColumn": 13, "fix": "81"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 93, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 93, "endColumn": 13, "fix": "82"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 20, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 20, "endColumn": 9, "fix": "83"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 29, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 29, "endColumn": 9, "fix": "84"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 38, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 38, "endColumn": 9, "fix": "85"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 47, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 47, "endColumn": 9, "fix": "86"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 58, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 58, "endColumn": 9, "fix": "87"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 70, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 70, "endColumn": 9, "fix": "88"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 82, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 82, "endColumn": 7, "fix": "89"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 105, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 105, "endColumn": 5, "fix": "90"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 147, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 147, "endColumn": 9, "fix": "91"}, {"ruleId": "92", "severity": 2, "message": "93", "line": 17, "column": 22, "nodeType": "94", "messageId": "95", "endLine": 17, "endColumn": 37, "fix": "96"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 31, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 31, "endColumn": 5, "fix": "97"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 39, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 39, "endColumn": 7, "fix": "98"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 45, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 45, "endColumn": 7, "fix": "99"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 53, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 53, "endColumn": 5, "fix": "100"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 80, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 80, "endColumn": 5, "fix": "101"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 86, "column": 1, "nodeType": "69", "messageId": "70", "endLine": 86, "endColumn": 5, "fix": "102"}, "no-trailing-spaces", "Trailing spaces not allowed.", "Program", "trailingSpace", {"range": "103", "text": "104"}, {"range": "105", "text": "104"}, {"range": "106", "text": "104"}, {"range": "107", "text": "104"}, {"range": "108", "text": "104"}, {"range": "109", "text": "104"}, {"range": "110", "text": "104"}, {"range": "111", "text": "104"}, {"range": "112", "text": "104"}, {"range": "113", "text": "104"}, {"range": "114", "text": "104"}, {"range": "115", "text": "104"}, {"range": "116", "text": "104"}, {"range": "117", "text": "104"}, {"range": "118", "text": "104"}, {"range": "119", "text": "104"}, {"range": "120", "text": "104"}, {"range": "121", "text": "104"}, {"range": "122", "text": "104"}, {"range": "123", "text": "104"}, {"range": "124", "text": "104"}, "dot-notation", "[\"Authorization\"] is better written in dot notation.", "Literal", "useDot", {"range": "125", "text": "126"}, {"range": "127", "text": "104"}, {"range": "128", "text": "104"}, {"range": "129", "text": "104"}, {"range": "130", "text": "104"}, {"range": "131", "text": "104"}, {"range": "132", "text": "104"}, [884, 886], "", [685, 691], [901, 913], [1796, 1800], [2092, 2096], [2178, 2184], [513, 521], [852, 860], [1148, 1154], [2069, 2077], [2301, 2313], [2356, 2368], [528, 536], [767, 775], [1005, 1013], [1259, 1267], [1555, 1563], [1914, 1922], [2213, 2219], [2691, 2695], [4022, 4030], [381, 398], ".Authorization", [625, 629], [798, 804], [940, 946], [1097, 1101], [1739, 1743], [1822, 1826]]