{"ast": null, "code": "'use strict';\n\nrequire(\"core-js/modules/es.array.push.js\");\nexports.__esModule = true;\nexports.default = scrollIntoView;\nvar _vue = require('vue');\nvar _vue2 = _interopRequireDefault(_vue);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction scrollIntoView(container, selected) {\n  if (_vue2.default.prototype.$isServer) return;\n  if (!selected) {\n    container.scrollTop = 0;\n    return;\n  }\n  var offsetParents = [];\n  var pointer = selected.offsetParent;\n  while (pointer && container !== pointer && container.contains(pointer)) {\n    offsetParents.push(pointer);\n    pointer = pointer.offsetParent;\n  }\n  var top = selected.offsetTop + offsetParents.reduce(function (prev, curr) {\n    return prev + curr.offsetTop;\n  }, 0);\n  var bottom = top + selected.offsetHeight;\n  var viewRectTop = container.scrollTop;\n  var viewRectBottom = viewRectTop + container.clientHeight;\n  if (top < viewRectTop) {\n    container.scrollTop = top;\n  } else if (bottom > viewRectBottom) {\n    container.scrollTop = bottom - container.clientHeight;\n  }\n}", "map": {"version": 3, "names": ["require", "exports", "__esModule", "default", "scrollIntoView", "_vue", "_vue2", "_interopRequireDefault", "obj", "container", "selected", "prototype", "$isServer", "scrollTop", "offsetParents", "pointer", "offsetParent", "contains", "push", "top", "offsetTop", "reduce", "prev", "curr", "bottom", "offsetHeight", "viewRectTop", "viewRectBottom", "clientHeight"], "sources": ["E:/java毕设/胜利投资理财网站/frontend/node_modules/element-ui/lib/utils/scroll-into-view.js"], "sourcesContent": ["'use strict';\n\nexports.__esModule = true;\nexports.default = scrollIntoView;\n\nvar _vue = require('vue');\n\nvar _vue2 = _interopRequireDefault(_vue);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction scrollIntoView(container, selected) {\n  if (_vue2.default.prototype.$isServer) return;\n\n  if (!selected) {\n    container.scrollTop = 0;\n    return;\n  }\n\n  var offsetParents = [];\n  var pointer = selected.offsetParent;\n  while (pointer && container !== pointer && container.contains(pointer)) {\n    offsetParents.push(pointer);\n    pointer = pointer.offsetParent;\n  }\n  var top = selected.offsetTop + offsetParents.reduce(function (prev, curr) {\n    return prev + curr.offsetTop;\n  }, 0);\n  var bottom = top + selected.offsetHeight;\n  var viewRectTop = container.scrollTop;\n  var viewRectBottom = viewRectTop + container.clientHeight;\n\n  if (top < viewRectTop) {\n    container.scrollTop = top;\n  } else if (bottom > viewRectBottom) {\n    container.scrollTop = bottom - container.clientHeight;\n  }\n}"], "mappings": "AAAA,YAAY;;AAACA,OAAA;AAEbC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAGC,cAAc;AAEhC,IAAIC,IAAI,GAAGL,OAAO,CAAC,KAAK,CAAC;AAEzB,IAAIM,KAAK,GAAGC,sBAAsB,CAACF,IAAI,CAAC;AAExC,SAASE,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACN,UAAU,GAAGM,GAAG,GAAG;IAAEL,OAAO,EAAEK;EAAI,CAAC;AAAE;AAE9F,SAASJ,cAAcA,CAACK,SAAS,EAAEC,QAAQ,EAAE;EAC3C,IAAIJ,KAAK,CAACH,OAAO,CAACQ,SAAS,CAACC,SAAS,EAAE;EAEvC,IAAI,CAACF,QAAQ,EAAE;IACbD,SAAS,CAACI,SAAS,GAAG,CAAC;IACvB;EACF;EAEA,IAAIC,aAAa,GAAG,EAAE;EACtB,IAAIC,OAAO,GAAGL,QAAQ,CAACM,YAAY;EACnC,OAAOD,OAAO,IAAIN,SAAS,KAAKM,OAAO,IAAIN,SAAS,CAACQ,QAAQ,CAACF,OAAO,CAAC,EAAE;IACtED,aAAa,CAACI,IAAI,CAACH,OAAO,CAAC;IAC3BA,OAAO,GAAGA,OAAO,CAACC,YAAY;EAChC;EACA,IAAIG,GAAG,GAAGT,QAAQ,CAACU,SAAS,GAAGN,aAAa,CAACO,MAAM,CAAC,UAAUC,IAAI,EAAEC,IAAI,EAAE;IACxE,OAAOD,IAAI,GAAGC,IAAI,CAACH,SAAS;EAC9B,CAAC,EAAE,CAAC,CAAC;EACL,IAAII,MAAM,GAAGL,GAAG,GAAGT,QAAQ,CAACe,YAAY;EACxC,IAAIC,WAAW,GAAGjB,SAAS,CAACI,SAAS;EACrC,IAAIc,cAAc,GAAGD,WAAW,GAAGjB,SAAS,CAACmB,YAAY;EAEzD,IAAIT,GAAG,GAAGO,WAAW,EAAE;IACrBjB,SAAS,CAACI,SAAS,GAAGM,GAAG;EAC3B,CAAC,MAAM,IAAIK,MAAM,GAAGG,cAAc,EAAE;IAClClB,SAAS,CAACI,SAAS,GAAGW,MAAM,GAAGf,SAAS,CAACmB,YAAY;EACvD;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}