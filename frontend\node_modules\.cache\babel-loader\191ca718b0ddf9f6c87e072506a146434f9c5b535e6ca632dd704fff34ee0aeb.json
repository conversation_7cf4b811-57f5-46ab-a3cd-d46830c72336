{"ast": null, "code": "import rules from '../rule/';\nimport { isEmptyValue } from '../util';\n\n/**\n *  Validates a function.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param callback The callback function.\n *  @param source The source object being validated.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\nfunction method(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n}\nexport default method;", "map": {"version": 3, "names": ["rules", "isEmptyValue", "method", "rule", "value", "callback", "source", "options", "errors", "validate", "required", "hasOwnProperty", "field", "undefined", "type"], "sources": ["E:/java毕设/胜利投资理财网站/frontend/node_modules/async-validator/es/validator/method.js"], "sourcesContent": ["import rules from '../rule/';\nimport { isEmptyValue } from '../util';\n\n/**\n *  Validates a function.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param callback The callback function.\n *  @param source The source object being validated.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\nfunction method(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n}\n\nexport default method;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,UAAU;AAC5B,SAASC,YAAY,QAAQ,SAAS;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACtD,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,QAAQ,GAAGN,IAAI,CAACO,QAAQ,IAAI,CAACP,IAAI,CAACO,QAAQ,IAAIJ,MAAM,CAACK,cAAc,CAACR,IAAI,CAACS,KAAK,CAAC;EACnF,IAAIH,QAAQ,EAAE;IACZ,IAAIR,YAAY,CAACG,KAAK,CAAC,IAAI,CAACD,IAAI,CAACO,QAAQ,EAAE;MACzC,OAAOL,QAAQ,CAAC,CAAC;IACnB;IACAL,KAAK,CAACU,QAAQ,CAACP,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEE,MAAM,EAAED,OAAO,CAAC;IACpD,IAAIH,KAAK,KAAKS,SAAS,EAAE;MACvBb,KAAK,CAACc,IAAI,CAACX,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEE,MAAM,EAAED,OAAO,CAAC;IAClD;EACF;EACAF,QAAQ,CAACG,MAAM,CAAC;AAClB;AAEA,eAAeN,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}