package com.investment.service.impl;

import com.investment.dto.LoginRequest;
import com.investment.dto.RegisterRequest;
import com.investment.dto.Result;
import com.investment.entity.User;
import com.investment.mapper.UserMapper;
import com.investment.service.UserService;
import com.investment.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    @Override
    public Result<String> register(RegisterRequest registerRequest) {
        try {
            // 验证密码确认
            if (!registerRequest.getPassword().equals(registerRequest.getConfirmPassword())) {
                return Result.badRequest("两次输入的密码不一致");
            }
            
            // 检查用户名是否已存在
            if (userMapper.existsByUsername(registerRequest.getUsername())) {
                return Result.badRequest("用户名已存在");
            }
            
            // 检查邮箱是否已存在
            if (userMapper.existsByEmail(registerRequest.getEmail())) {
                return Result.badRequest("邮箱已被注册");
            }
            
            // 检查手机号是否已存在
            if (userMapper.existsByPhone(registerRequest.getPhone())) {
                return Result.badRequest("手机号已被注册");
            }
            
            // 创建新用户
            User user = new User();
            user.setUsername(registerRequest.getUsername());
            user.setPassword(encodePassword(registerRequest.getPassword()));
            user.setEmail(registerRequest.getEmail());
            user.setPhone(registerRequest.getPhone());
            user.setRealName(registerRequest.getRealName());
            user.setStatus(1);
            user.setBalance(0.0);
            user.setCreateTime(LocalDateTime.now());
            user.setUpdateTime(LocalDateTime.now());
            user.setDeleted(0);
            
            // 保存用户
            int result = userMapper.insert(user);
            if (result > 0) {
                log.info("用户注册成功：{}", registerRequest.getUsername());
                return Result.success("注册成功");
            } else {
                return Result.error("注册失败");
            }
            
        } catch (Exception e) {
            log.error("用户注册异常：", e);
            return Result.error("注册失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<String> login(LoginRequest loginRequest) {
        try {
            // 查询用户
            User user = userMapper.findByUsername(loginRequest.getUsername());
            if (user == null) {
                return Result.badRequest("用户名或密码错误");
            }
            
            // 检查用户状态
            if (user.getStatus() == 0) {
                return Result.badRequest("账户已被禁用");
            }
            
            // 验证密码
            if (!verifyPassword(loginRequest.getPassword(), user.getPassword())) {
                return Result.badRequest("用户名或密码错误");
            }
            
            // 生成JWT token
            String token = jwtUtil.generateToken(user.getId(), user.getUsername());
            
            log.info("用户登录成功：{}", loginRequest.getUsername());
            return Result.success("登录成功", token);
            
        } catch (Exception e) {
            log.error("用户登录异常：", e);
            return Result.error("登录失败：" + e.getMessage());
        }
    }
    
    @Override
    public User findByUsername(String username) {
        return userMapper.findByUsername(username);
    }
    
    @Override
    public User findById(Long id) {
        return userMapper.selectById(id);
    }
    
    @Override
    public boolean verifyPassword(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }
    
    @Override
    public String encodePassword(String password) {
        return passwordEncoder.encode(password);
    }
}
