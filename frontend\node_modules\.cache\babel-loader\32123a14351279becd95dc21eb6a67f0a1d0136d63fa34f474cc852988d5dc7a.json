{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { register } from '@/api/user';\nexport default {\n  name: 'Register',\n  data() {\n    // 自定义验证规则\n    const validateConfirmPassword = (rule, value, callback) => {\n      if (value !== this.registerForm.password) {\n        callback(new Error('两次输入的密码不一致'));\n      } else {\n        callback();\n      }\n    };\n    return {\n      registerForm: {\n        username: '',\n        email: '',\n        phone: '',\n        realName: '',\n        password: '',\n        confirmPassword: ''\n      },\n      registerRules: {\n        username: [{\n          required: true,\n          message: '请输入用户名',\n          trigger: 'blur'\n        }, {\n          min: 3,\n          max: 20,\n          message: '用户名长度在 3 到 20 个字符',\n          trigger: 'blur'\n        }, {\n          pattern: /^[a-zA-Z0-9_]+$/,\n          message: '用户名只能包含字母、数字和下划线',\n          trigger: 'blur'\n        }],\n        email: [{\n          required: true,\n          message: '请输入邮箱',\n          trigger: 'blur'\n        }, {\n          type: 'email',\n          message: '请输入正确的邮箱格式',\n          trigger: 'blur'\n        }],\n        phone: [{\n          required: true,\n          message: '请输入手机号',\n          trigger: 'blur'\n        }, {\n          pattern: /^1[3-9]\\d{9}$/,\n          message: '请输入正确的手机号格式',\n          trigger: 'blur'\n        }],\n        password: [{\n          required: true,\n          message: '请输入密码',\n          trigger: 'blur'\n        }, {\n          min: 6,\n          max: 20,\n          message: '密码长度在 6 到 20 个字符',\n          trigger: 'blur'\n        }],\n        confirmPassword: [{\n          required: true,\n          message: '请确认密码',\n          trigger: 'blur'\n        }, {\n          validator: validateConfirmPassword,\n          trigger: 'blur'\n        }]\n      },\n      loading: false\n    };\n  },\n  methods: {\n    handleRegister() {\n      this.$refs.registerForm.validate(async valid => {\n        if (!valid) {\n          return false;\n        }\n        this.loading = true;\n        try {\n          const response = await register(this.registerForm);\n          if (response.code === 200) {\n            this.$message.success('注册成功，请登录');\n            this.$router.push('/login');\n          } else {\n            this.$message.error(response.message || '注册失败');\n          }\n        } catch (error) {\n          console.error('注册错误：', error);\n          this.$message.error('注册失败，请检查网络连接');\n        } finally {\n          this.loading = false;\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["register", "name", "data", "validateConfirmPassword", "rule", "value", "callback", "registerForm", "password", "Error", "username", "email", "phone", "realName", "confirmPassword", "registerRules", "required", "message", "trigger", "min", "max", "pattern", "type", "validator", "loading", "methods", "handleRegister", "$refs", "validate", "valid", "response", "code", "$message", "success", "$router", "push", "error", "console"], "sources": ["src/views/Register.vue"], "sourcesContent": ["<template>\n  <div class=\"auth-container\">\n    <div class=\"auth-card\">\n      <h2 class=\"auth-title\">用户注册</h2>\n      <el-form\n        ref=\"registerForm\"\n        :model=\"registerForm\"\n        :rules=\"registerRules\"\n        class=\"auth-form\"\n        @submit.native.prevent=\"handleRegister\"\n      >\n        <el-form-item prop=\"username\">\n          <el-input\n            v-model=\"registerForm.username\"\n            placeholder=\"请输入用户名\"\n            prefix-icon=\"el-icon-user\"\n            clearable\n          />\n        </el-form-item>\n        \n        <el-form-item prop=\"email\">\n          <el-input\n            v-model=\"registerForm.email\"\n            placeholder=\"请输入邮箱\"\n            prefix-icon=\"el-icon-message\"\n            clearable\n          />\n        </el-form-item>\n        \n        <el-form-item prop=\"phone\">\n          <el-input\n            v-model=\"registerForm.phone\"\n            placeholder=\"请输入手机号\"\n            prefix-icon=\"el-icon-phone\"\n            clearable\n          />\n        </el-form-item>\n        \n        <el-form-item prop=\"realName\">\n          <el-input\n            v-model=\"registerForm.realName\"\n            placeholder=\"请输入真实姓名（可选）\"\n            prefix-icon=\"el-icon-user-solid\"\n            clearable\n          />\n        </el-form-item>\n        \n        <el-form-item prop=\"password\">\n          <el-input\n            v-model=\"registerForm.password\"\n            type=\"password\"\n            placeholder=\"请输入密码\"\n            prefix-icon=\"el-icon-lock\"\n            show-password\n            clearable\n          />\n        </el-form-item>\n        \n        <el-form-item prop=\"confirmPassword\">\n          <el-input\n            v-model=\"registerForm.confirmPassword\"\n            type=\"password\"\n            placeholder=\"请确认密码\"\n            prefix-icon=\"el-icon-lock\"\n            show-password\n            clearable\n            @keyup.enter.native=\"handleRegister\"\n          />\n        </el-form-item>\n        \n        <el-form-item>\n          <el-button\n            type=\"primary\"\n            class=\"auth-button\"\n            :loading=\"loading\"\n            @click=\"handleRegister\"\n          >\n            {{ loading ? '注册中...' : '注册' }}\n          </el-button>\n        </el-form-item>\n      </el-form>\n      \n      <div class=\"auth-link\">\n        <span>已有账号？</span>\n        <router-link to=\"/login\">立即登录</router-link>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { register } from '@/api/user'\n\nexport default {\n  name: 'Register',\n  data() {\n    // 自定义验证规则\n    const validateConfirmPassword = (rule, value, callback) => {\n      if (value !== this.registerForm.password) {\n        callback(new Error('两次输入的密码不一致'))\n      } else {\n        callback()\n      }\n    }\n    \n    return {\n      registerForm: {\n        username: '',\n        email: '',\n        phone: '',\n        realName: '',\n        password: '',\n        confirmPassword: ''\n      },\n      registerRules: {\n        username: [\n          { required: true, message: '请输入用户名', trigger: 'blur' },\n          { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },\n          { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }\n        ],\n        email: [\n          { required: true, message: '请输入邮箱', trigger: 'blur' },\n          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }\n        ],\n        phone: [\n          { required: true, message: '请输入手机号', trigger: 'blur' },\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }\n        ],\n        password: [\n          { required: true, message: '请输入密码', trigger: 'blur' },\n          { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }\n        ],\n        confirmPassword: [\n          { required: true, message: '请确认密码', trigger: 'blur' },\n          { validator: validateConfirmPassword, trigger: 'blur' }\n        ]\n      },\n      loading: false\n    }\n  },\n  methods: {\n    handleRegister() {\n      this.$refs.registerForm.validate(async (valid) => {\n        if (!valid) {\n          return false\n        }\n        \n        this.loading = true\n        try {\n          const response = await register(this.registerForm)\n          if (response.code === 200) {\n            this.$message.success('注册成功，请登录')\n            this.$router.push('/login')\n          } else {\n            this.$message.error(response.message || '注册失败')\n          }\n        } catch (error) {\n          console.error('注册错误：', error)\n          this.$message.error('注册失败，请检查网络连接')\n        } finally {\n          this.loading = false\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.auth-card {\n  width: 450px;\n}\n</style>\n"], "mappings": ";AA2FA,SAAAA,QAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;IACA,MAAAC,uBAAA,GAAAA,CAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAD,KAAA,UAAAE,YAAA,CAAAC,QAAA;QACAF,QAAA,KAAAG,KAAA;MACA;QACAH,QAAA;MACA;IACA;IAEA;MACAC,YAAA;QACAG,QAAA;QACAC,KAAA;QACAC,KAAA;QACAC,QAAA;QACAL,QAAA;QACAM,eAAA;MACA;MACAC,aAAA;QACAL,QAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAG,OAAA;UAAAJ,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,KAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAI,IAAA;UAAAL,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,KAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAG,OAAA;UAAAJ,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,QAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAJ,eAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAK,SAAA,EAAApB,uBAAA;UAAAe,OAAA;QAAA;MAEA;MACAM,OAAA;IACA;EACA;EACAC,OAAA;IACAC,eAAA;MACA,KAAAC,KAAA,CAAApB,YAAA,CAAAqB,QAAA,OAAAC,KAAA;QACA,KAAAA,KAAA;UACA;QACA;QAEA,KAAAL,OAAA;QACA;UACA,MAAAM,QAAA,SAAA9B,QAAA,MAAAO,YAAA;UACA,IAAAuB,QAAA,CAAAC,IAAA;YACA,KAAAC,QAAA,CAAAC,OAAA;YACA,KAAAC,OAAA,CAAAC,IAAA;UACA;YACA,KAAAH,QAAA,CAAAI,KAAA,CAAAN,QAAA,CAAAb,OAAA;UACA;QACA,SAAAmB,KAAA;UACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;UACA,KAAAJ,QAAA,CAAAI,KAAA;QACA;UACA,KAAAZ,OAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}