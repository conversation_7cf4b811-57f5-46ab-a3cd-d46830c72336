{"ast": null, "code": "import Vue from 'vue';\nimport VueRouter from 'vue-router';\nimport Cookies from 'js-cookie';\nVue.use(VueRouter);\nconst routes = [{\n  path: '/',\n  redirect: '/login'\n}, {\n  path: '/login',\n  name: 'Login',\n  component: () => import('@/views/Login.vue'),\n  meta: {\n    title: '用户登录',\n    requiresAuth: false\n  }\n}, {\n  path: '/register',\n  name: 'Register',\n  component: () => import('@/views/Register.vue'),\n  meta: {\n    title: '用户注册',\n    requiresAuth: false\n  }\n}, {\n  path: '/dashboard',\n  name: 'Dashboard',\n  component: () => import('@/views/Dashboard.vue'),\n  meta: {\n    title: '控制台',\n    requiresAuth: true\n  }\n}];\nconst router = new VueRouter({\n  mode: 'history',\n  base: process.env.BASE_URL,\n  routes\n});\n\n// 路由守卫\nrouter.beforeEach((to, from, next) => {\n  // 设置页面标题\n  document.title = to.meta.title || '胜利投资理财网站';\n\n  // 检查是否需要登录\n  if (to.meta.requiresAuth) {\n    const token = Cookies.get('token');\n    if (!token) {\n      // 未登录，跳转到登录页\n      next({\n        path: '/login',\n        query: {\n          redirect: to.fullPath\n        }\n      });\n    } else {\n      next();\n    }\n  } else {\n    // 如果已登录且访问登录/注册页，跳转到控制台\n    const token = Cookies.get('token');\n    if (token && (to.path === '/login' || to.path === '/register')) {\n      next('/dashboard');\n    } else {\n      next();\n    }\n  }\n});\nexport default router;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Cookies", "use", "routes", "path", "redirect", "name", "component", "meta", "title", "requiresAuth", "router", "mode", "base", "process", "env", "BASE_URL", "beforeEach", "to", "from", "next", "document", "token", "get", "query", "fullPath"], "sources": ["E:/java毕设/胜利投资理财网站/frontend/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport VueRouter from 'vue-router'\nimport Cookies from 'js-cookie'\n\nVue.use(VueRouter)\n\nconst routes = [\n  {\n    path: '/',\n    redirect: '/login'\n  },\n  {\n    path: '/login',\n    name: 'Login',\n    component: () => import('@/views/Login.vue'),\n    meta: {\n      title: '用户登录',\n      requiresAuth: false\n    }\n  },\n  {\n    path: '/register',\n    name: 'Register',\n    component: () => import('@/views/Register.vue'),\n    meta: {\n      title: '用户注册',\n      requiresAuth: false\n    }\n  },\n  {\n    path: '/dashboard',\n    name: 'Dashboard',\n    component: () => import('@/views/Dashboard.vue'),\n    meta: {\n      title: '控制台',\n      requiresAuth: true\n    }\n  }\n]\n\nconst router = new VueRouter({\n  mode: 'history',\n  base: process.env.BASE_URL,\n  routes\n})\n\n// 路由守卫\nrouter.beforeEach((to, from, next) => {\n  // 设置页面标题\n  document.title = to.meta.title || '胜利投资理财网站'\n  \n  // 检查是否需要登录\n  if (to.meta.requiresAuth) {\n    const token = Cookies.get('token')\n    if (!token) {\n      // 未登录，跳转到登录页\n      next({\n        path: '/login',\n        query: { redirect: to.fullPath }\n      })\n    } else {\n      next()\n    }\n  } else {\n    // 如果已登录且访问登录/注册页，跳转到控制台\n    const token = Cookies.get('token')\n    if (token && (to.path === '/login' || to.path === '/register')) {\n      next('/dashboard')\n    } else {\n      next()\n    }\n  }\n})\n\nexport default router\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,OAAO,MAAM,WAAW;AAE/BF,GAAG,CAACG,GAAG,CAACF,SAAS,CAAC;AAElB,MAAMG,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,QAAQ,EAAE;AACZ,CAAC,EACD;EACED,IAAI,EAAE,QAAQ;EACdE,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC;EAC5CC,IAAI,EAAE;IACJC,KAAK,EAAE,MAAM;IACbC,YAAY,EAAE;EAChB;AACF,CAAC,EACD;EACEN,IAAI,EAAE,WAAW;EACjBE,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC;EAC/CC,IAAI,EAAE;IACJC,KAAK,EAAE,MAAM;IACbC,YAAY,EAAE;EAChB;AACF,CAAC,EACD;EACEN,IAAI,EAAE,YAAY;EAClBE,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,uBAAuB,CAAC;EAChDC,IAAI,EAAE;IACJC,KAAK,EAAE,KAAK;IACZC,YAAY,EAAE;EAChB;AACF,CAAC,CACF;AAED,MAAMC,MAAM,GAAG,IAAIX,SAAS,CAAC;EAC3BY,IAAI,EAAE,SAAS;EACfC,IAAI,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ;EAC1Bb;AACF,CAAC,CAAC;;AAEF;AACAQ,MAAM,CAACM,UAAU,CAAC,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EACpC;EACAC,QAAQ,CAACZ,KAAK,GAAGS,EAAE,CAACV,IAAI,CAACC,KAAK,IAAI,UAAU;;EAE5C;EACA,IAAIS,EAAE,CAACV,IAAI,CAACE,YAAY,EAAE;IACxB,MAAMY,KAAK,GAAGrB,OAAO,CAACsB,GAAG,CAAC,OAAO,CAAC;IAClC,IAAI,CAACD,KAAK,EAAE;MACV;MACAF,IAAI,CAAC;QACHhB,IAAI,EAAE,QAAQ;QACdoB,KAAK,EAAE;UAAEnB,QAAQ,EAAEa,EAAE,CAACO;QAAS;MACjC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLL,IAAI,CAAC,CAAC;IACR;EACF,CAAC,MAAM;IACL;IACA,MAAME,KAAK,GAAGrB,OAAO,CAACsB,GAAG,CAAC,OAAO,CAAC;IAClC,IAAID,KAAK,KAAKJ,EAAE,CAACd,IAAI,KAAK,QAAQ,IAAIc,EAAE,CAACd,IAAI,KAAK,WAAW,CAAC,EAAE;MAC9DgB,IAAI,CAAC,YAAY,CAAC;IACpB,CAAC,MAAM;MACLA,IAAI,CAAC,CAAC;IACR;EACF;AACF,CAAC,CAAC;AAEF,eAAeT,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}