{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"dashboard\"\n  }, [_c(\"el-container\", [_c(\"el-header\", {\n    staticClass: \"header\"\n  }, [_c(\"div\", {\n    staticClass: \"header-left\"\n  }, [_c(\"h2\", [_vm._v(\"胜利投资理财网站\")])]), _c(\"div\", {\n    staticClass: \"header-right\"\n  }, [_c(\"el-dropdown\", {\n    on: {\n      command: _vm.handleCommand\n    }\n  }, [_c(\"span\", {\n    staticClass: \"el-dropdown-link\"\n  }, [_vm._v(\" 欢迎您 \"), _c(\"i\", {\n    staticClass: \"el-icon-arrow-down el-icon--right\"\n  })]), _c(\"el-dropdown-menu\", {\n    attrs: {\n      slot: \"dropdown\"\n    },\n    slot: \"dropdown\"\n  }, [_c(\"el-dropdown-item\", {\n    attrs: {\n      command: \"profile\"\n    }\n  }, [_vm._v(\"个人中心\")]), _c(\"el-dropdown-item\", {\n    attrs: {\n      command: \"logout\"\n    }\n  }, [_vm._v(\"退出登录\")])], 1)], 1)], 1)]), _c(\"el-main\", {\n    staticClass: \"main\"\n  }, [_c(\"div\", {\n    staticClass: \"welcome-card\"\n  }, [_c(\"el-card\", [_c(\"h3\", [_vm._v(\"欢迎来到胜利投资理财网站\")]), _c(\"p\", [_vm._v(\"您已成功登录系统！\")]), _c(\"p\", [_vm._v(\"这里是用户控制台，后续可以添加更多功能模块。\")]), _c(\"div\", {\n    staticClass: \"test-section\"\n  }, [_c(\"h4\", [_vm._v(\"测试后端连接：\")]), _c(\"el-button\", {\n    attrs: {\n      type: \"primary\",\n      loading: _vm.testing\n    },\n    on: {\n      click: _vm.testBackend\n    }\n  }, [_vm._v(\" 测试后端接口 \")]), _vm.testResult ? _c(\"p\", {\n    class: _vm.testResult.success ? \"success\" : \"error\"\n  }, [_vm._v(\" \" + _vm._s(_vm.testResult.message) + \" \")]) : _vm._e()], 1)])], 1)])], 1)], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "on", "command", "handleCommand", "attrs", "slot", "type", "loading", "testing", "click", "testBackend", "testResult", "class", "success", "_s", "message", "_e", "staticRenderFns", "_withStripped"], "sources": ["E:/java毕设/胜利投资理财网站/frontend/src/views/Dashboard.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"dashboard\" },\n    [\n      _c(\n        \"el-container\",\n        [\n          _c(\"el-header\", { staticClass: \"header\" }, [\n            _c(\"div\", { staticClass: \"header-left\" }, [\n              _c(\"h2\", [_vm._v(\"胜利投资理财网站\")]),\n            ]),\n            _c(\n              \"div\",\n              { staticClass: \"header-right\" },\n              [\n                _c(\n                  \"el-dropdown\",\n                  { on: { command: _vm.handleCommand } },\n                  [\n                    _c(\"span\", { staticClass: \"el-dropdown-link\" }, [\n                      _vm._v(\" 欢迎您 \"),\n                      _c(\"i\", {\n                        staticClass: \"el-icon-arrow-down el-icon--right\",\n                      }),\n                    ]),\n                    _c(\n                      \"el-dropdown-menu\",\n                      { attrs: { slot: \"dropdown\" }, slot: \"dropdown\" },\n                      [\n                        _c(\n                          \"el-dropdown-item\",\n                          { attrs: { command: \"profile\" } },\n                          [_vm._v(\"个人中心\")]\n                        ),\n                        _c(\n                          \"el-dropdown-item\",\n                          { attrs: { command: \"logout\" } },\n                          [_vm._v(\"退出登录\")]\n                        ),\n                      ],\n                      1\n                    ),\n                  ],\n                  1\n                ),\n              ],\n              1\n            ),\n          ]),\n          _c(\"el-main\", { staticClass: \"main\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"welcome-card\" },\n              [\n                _c(\"el-card\", [\n                  _c(\"h3\", [_vm._v(\"欢迎来到胜利投资理财网站\")]),\n                  _c(\"p\", [_vm._v(\"您已成功登录系统！\")]),\n                  _c(\"p\", [\n                    _vm._v(\"这里是用户控制台，后续可以添加更多功能模块。\"),\n                  ]),\n                  _c(\n                    \"div\",\n                    { staticClass: \"test-section\" },\n                    [\n                      _c(\"h4\", [_vm._v(\"测试后端连接：\")]),\n                      _c(\n                        \"el-button\",\n                        {\n                          attrs: { type: \"primary\", loading: _vm.testing },\n                          on: { click: _vm.testBackend },\n                        },\n                        [_vm._v(\" 测试后端接口 \")]\n                      ),\n                      _vm.testResult\n                        ? _c(\n                            \"p\",\n                            {\n                              class: _vm.testResult.success\n                                ? \"success\"\n                                : \"error\",\n                            },\n                            [_vm._v(\" \" + _vm._s(_vm.testResult.message) + \" \")]\n                          )\n                        : _vm._e(),\n                    ],\n                    1\n                  ),\n                ]),\n              ],\n              1\n            ),\n          ]),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5B,CACEF,EAAE,CACA,cAAc,EACd,CACEA,EAAE,CAAC,WAAW,EAAE;IAAEE,WAAW,EAAE;EAAS,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAC/B,CAAC,EACFH,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CACEF,EAAE,CACA,aAAa,EACb;IAAEI,EAAE,EAAE;MAAEC,OAAO,EAAEN,GAAG,CAACO;IAAc;EAAE,CAAC,EACtC,CACEN,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC9CH,GAAG,CAACI,EAAE,CAAC,OAAO,CAAC,EACfH,EAAE,CAAC,GAAG,EAAE;IACNE,WAAW,EAAE;EACf,CAAC,CAAC,CACH,CAAC,EACFF,EAAE,CACA,kBAAkB,EAClB;IAAEO,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAW,CAAC;IAAEA,IAAI,EAAE;EAAW,CAAC,EACjD,CACER,EAAE,CACA,kBAAkB,EAClB;IAAEO,KAAK,EAAE;MAAEF,OAAO,EAAE;IAAU;EAAE,CAAC,EACjC,CAACN,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDH,EAAE,CACA,kBAAkB,EAClB;IAAEO,KAAK,EAAE;MAAEF,OAAO,EAAE;IAAS;EAAE,CAAC,EAChC,CAACN,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,EACFH,EAAE,CAAC,SAAS,EAAE;IAAEE,WAAW,EAAE;EAAO,CAAC,EAAE,CACrCF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CACEF,EAAE,CAAC,SAAS,EAAE,CACZA,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,EAClCH,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,EAC9BH,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAACI,EAAE,CAAC,wBAAwB,CAAC,CACjC,CAAC,EACFH,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CACEF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,EAC7BH,EAAE,CACA,WAAW,EACX;IACEO,KAAK,EAAE;MAAEE,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAEX,GAAG,CAACY;IAAQ,CAAC;IAChDP,EAAE,EAAE;MAAEQ,KAAK,EAAEb,GAAG,CAACc;IAAY;EAC/B,CAAC,EACD,CAACd,GAAG,CAACI,EAAE,CAAC,UAAU,CAAC,CACrB,CAAC,EACDJ,GAAG,CAACe,UAAU,GACVd,EAAE,CACA,GAAG,EACH;IACEe,KAAK,EAAEhB,GAAG,CAACe,UAAU,CAACE,OAAO,GACzB,SAAS,GACT;EACN,CAAC,EACD,CAACjB,GAAG,CAACI,EAAE,CAAC,GAAG,GAAGJ,GAAG,CAACkB,EAAE,CAAClB,GAAG,CAACe,UAAU,CAACI,OAAO,CAAC,GAAG,GAAG,CAAC,CACrD,CAAC,GACDnB,GAAG,CAACoB,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC,CACF,CAAC,CACH,EACD,CACF,CAAC,CACF,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBtB,MAAM,CAACuB,aAAa,GAAG,IAAI;AAE3B,SAASvB,MAAM,EAAEsB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}