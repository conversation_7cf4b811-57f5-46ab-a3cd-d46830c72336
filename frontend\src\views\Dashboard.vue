<template>
  <div class="dashboard">
    <el-container>
      <!-- 头部 -->
      <el-header class="header">
        <div class="header-left">
          <h2>胜利投资理财网站</h2>
        </div>
        <div class="header-right">
          <el-dropdown @command="handleCommand">
            <span class="el-dropdown-link">
              欢迎您 <i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="profile">个人中心</el-dropdown-item>
              <el-dropdown-item command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </el-header>
      
      <!-- 主体内容 -->
      <el-main class="main">
        <div class="welcome-card">
          <el-card>
            <h3>欢迎来到胜利投资理财网站</h3>
            <p>您已成功登录系统！</p>
            <p>这里是用户控制台，后续可以添加更多功能模块。</p>
            
            <div class="test-section">
              <h4>测试后端连接：</h4>
              <el-button type="primary" @click="testBackend" :loading="testing">
                测试后端接口
              </el-button>
              <p v-if="testResult" :class="testResult.success ? 'success' : 'error'">
                {{ testResult.message }}
              </p>
            </div>
          </el-card>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import { test } from '@/api/user'
import Cookies from 'js-cookie'

export default {
  name: 'Dashboard',
  data() {
    return {
      testing: false,
      testResult: null
    }
  },
  methods: {
    handleCommand(command) {
      switch (command) {
        case 'profile':
          this.$message.info('个人中心功能待开发')
          break
        case 'logout':
          this.logout()
          break
      }
    },
    
    logout() {
      this.$confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        Cookies.remove('token')
        this.$message.success('退出登录成功')
        this.$router.push('/login')
      })
    },
    
    async testBackend() {
      this.testing = true
      this.testResult = null
      
      try {
        const response = await test()
        this.testResult = {
          success: true,
          message: response.data || '后端连接成功'
        }
      } catch (error) {
        this.testResult = {
          success: false,
          message: '后端连接失败：' + error.message
        }
      } finally {
        this.testing = false
      }
    }
  }
}
</script>

<style scoped>
.dashboard {
  min-height: 100vh;
}

.header {
  background-color: #409eff;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.header-left h2 {
  margin: 0;
}

.header-right {
  cursor: pointer;
}

.el-dropdown-link {
  color: white;
}

.main {
  padding: 20px;
  background-color: #f5f5f5;
}

.welcome-card {
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.test-section h4 {
  margin-bottom: 15px;
}

.success {
  color: #67c23a;
  margin-top: 10px;
}

.error {
  color: #f56c6c;
  margin-top: 10px;
}
</style>
