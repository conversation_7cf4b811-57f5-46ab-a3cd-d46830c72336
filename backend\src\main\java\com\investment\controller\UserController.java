package com.investment.controller;

import com.investment.dto.LoginRequest;
import com.investment.dto.RegisterRequest;
import com.investment.dto.Result;
import com.investment.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 用户控制器
 */
@Slf4j
@RestController
@RequestMapping("/user")
@Validated
@CrossOrigin(origins = "*")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    /**
     * 用户注册
     * @param registerRequest 注册请求
     * @return 注册结果
     */
    @PostMapping("/register")
    public Result<String> register(@Valid @RequestBody RegisterRequest registerRequest) {
        log.info("用户注册请求：{}", registerRequest.getUsername());
        return userService.register(registerRequest);
    }
    
    /**
     * 用户登录
     * @param loginRequest 登录请求
     * @return 登录结果
     */
    @PostMapping("/login")
    public Result<String> login(@Valid @RequestBody LoginRequest loginRequest) {
        log.info("用户登录请求：{}", loginRequest.getUsername());
        return userService.login(loginRequest);
    }
    
    /**
     * 检查用户名是否可用
     * @param username 用户名
     * @return 检查结果
     */
    @GetMapping("/check-username")
    public Result<Boolean> checkUsername(@RequestParam String username) {
        boolean exists = userService.findByUsername(username) != null;
        return Result.success(!exists);
    }
    
    /**
     * 测试接口
     * @return 测试结果
     */
    @GetMapping("/test")
    public Result<String> test() {
        return Result.success("后端服务正常运行");
    }
}
