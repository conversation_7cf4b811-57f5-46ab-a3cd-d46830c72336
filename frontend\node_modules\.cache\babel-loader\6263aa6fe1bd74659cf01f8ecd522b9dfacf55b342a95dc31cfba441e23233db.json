{"ast": null, "code": "import axios from 'axios';\nimport { Message } from 'element-ui';\nimport Cookies from 'js-cookie';\n\n// 创建axios实例\nconst service = axios.create({\n  baseURL: process.env.NODE_ENV === 'production' ? '/api' : '/api',\n  timeout: 10000\n});\n\n// 请求拦截器\nservice.interceptors.request.use(config => {\n  // 添加token到请求头\n  const token = Cookies.get('token');\n  if (token) {\n    config.headers['Authorization'] = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  console.error('请求错误：', error);\n  return Promise.reject(error);\n});\n\n// 响应拦截器\nservice.interceptors.response.use(response => {\n  const res = response.data;\n\n  // 如果返回的状态码不是200，则显示错误信息\n  if (res.code !== 200) {\n    Message({\n      message: res.message || '请求失败',\n      type: 'error',\n      duration: 3000\n    });\n\n    // 401表示未授权，跳转到登录页\n    if (res.code === 401) {\n      Cookies.remove('token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(new Error(res.message || '请求失败'));\n  } else {\n    return res;\n  }\n}, error => {\n  console.error('响应错误：', error);\n  let message = '网络错误';\n  if (error.response) {\n    switch (error.response.status) {\n      case 400:\n        message = '请求参数错误';\n        break;\n      case 401:\n        message = '未授权，请重新登录';\n        Cookies.remove('token');\n        window.location.href = '/login';\n        break;\n      case 403:\n        message = '拒绝访问';\n        break;\n      case 404:\n        message = '请求地址不存在';\n        break;\n      case 500:\n        message = '服务器内部错误';\n        break;\n      default:\n        message = `连接错误${error.response.status}`;\n    }\n  } else if (error.code === 'ECONNABORTED') {\n    message = '请求超时';\n  }\n  Message({\n    message,\n    type: 'error',\n    duration: 3000\n  });\n  return Promise.reject(error);\n});\nexport default service;", "map": {"version": 3, "names": ["axios", "Message", "Cookies", "service", "create", "baseURL", "process", "env", "NODE_ENV", "timeout", "interceptors", "request", "use", "config", "token", "get", "headers", "error", "console", "Promise", "reject", "response", "res", "data", "code", "message", "type", "duration", "remove", "window", "location", "href", "Error", "status"], "sources": ["E:/java毕设/胜利投资理财网站/frontend/src/api/request.js"], "sourcesContent": ["import axios from 'axios'\nimport { Message } from 'element-ui'\nimport Cookies from 'js-cookie'\n\n// 创建axios实例\nconst service = axios.create({\n  baseURL: process.env.NODE_ENV === 'production' ? '/api' : '/api',\n  timeout: 10000\n})\n\n// 请求拦截器\nservice.interceptors.request.use(\n  config => {\n    // 添加token到请求头\n    const token = Cookies.get('token')\n    if (token) {\n      config.headers['Authorization'] = `Bearer ${token}`\n    }\n    return config\n  },\n  error => {\n    console.error('请求错误：', error)\n    return Promise.reject(error)\n  }\n)\n\n// 响应拦截器\nservice.interceptors.response.use(\n  response => {\n    const res = response.data\n    \n    // 如果返回的状态码不是200，则显示错误信息\n    if (res.code !== 200) {\n      Message({\n        message: res.message || '请求失败',\n        type: 'error',\n        duration: 3000\n      })\n      \n      // 401表示未授权，跳转到登录页\n      if (res.code === 401) {\n        Cookies.remove('token')\n        window.location.href = '/login'\n      }\n      \n      return Promise.reject(new Error(res.message || '请求失败'))\n    } else {\n      return res\n    }\n  },\n  error => {\n    console.error('响应错误：', error)\n    \n    let message = '网络错误'\n    if (error.response) {\n      switch (error.response.status) {\n        case 400:\n          message = '请求参数错误'\n          break\n        case 401:\n          message = '未授权，请重新登录'\n          Cookies.remove('token')\n          window.location.href = '/login'\n          break\n        case 403:\n          message = '拒绝访问'\n          break\n        case 404:\n          message = '请求地址不存在'\n          break\n        case 500:\n          message = '服务器内部错误'\n          break\n        default:\n          message = `连接错误${error.response.status}`\n      }\n    } else if (error.code === 'ECONNABORTED') {\n      message = '请求超时'\n    }\n    \n    Message({\n      message,\n      type: 'error',\n      duration: 3000\n    })\n    \n    return Promise.reject(error)\n  }\n)\n\nexport default service\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,YAAY;AACpC,OAAOC,OAAO,MAAM,WAAW;;AAE/B;AACA,MAAMC,OAAO,GAAGH,KAAK,CAACI,MAAM,CAAC;EAC3BC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,MAAM,GAAG,MAAM;EAChEC,OAAO,EAAE;AACX,CAAC,CAAC;;AAEF;AACAN,OAAO,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9BC,MAAM,IAAI;EACR;EACA,MAAMC,KAAK,GAAGZ,OAAO,CAACa,GAAG,CAAC,OAAO,CAAC;EAClC,IAAID,KAAK,EAAE;IACTD,MAAM,CAACG,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUF,KAAK,EAAE;EACrD;EACA,OAAOD,MAAM;AACf,CAAC,EACDI,KAAK,IAAI;EACPC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;EAC7B,OAAOE,OAAO,CAACC,MAAM,CAACH,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAd,OAAO,CAACO,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC/BS,QAAQ,IAAI;EACV,MAAMC,GAAG,GAAGD,QAAQ,CAACE,IAAI;;EAEzB;EACA,IAAID,GAAG,CAACE,IAAI,KAAK,GAAG,EAAE;IACpBvB,OAAO,CAAC;MACNwB,OAAO,EAAEH,GAAG,CAACG,OAAO,IAAI,MAAM;MAC9BC,IAAI,EAAE,OAAO;MACbC,QAAQ,EAAE;IACZ,CAAC,CAAC;;IAEF;IACA,IAAIL,GAAG,CAACE,IAAI,KAAK,GAAG,EAAE;MACpBtB,OAAO,CAAC0B,MAAM,CAAC,OAAO,CAAC;MACvBC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;IACjC;IAEA,OAAOZ,OAAO,CAACC,MAAM,CAAC,IAAIY,KAAK,CAACV,GAAG,CAACG,OAAO,IAAI,MAAM,CAAC,CAAC;EACzD,CAAC,MAAM;IACL,OAAOH,GAAG;EACZ;AACF,CAAC,EACDL,KAAK,IAAI;EACPC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;EAE7B,IAAIQ,OAAO,GAAG,MAAM;EACpB,IAAIR,KAAK,CAACI,QAAQ,EAAE;IAClB,QAAQJ,KAAK,CAACI,QAAQ,CAACY,MAAM;MAC3B,KAAK,GAAG;QACNR,OAAO,GAAG,QAAQ;QAClB;MACF,KAAK,GAAG;QACNA,OAAO,GAAG,WAAW;QACrBvB,OAAO,CAAC0B,MAAM,CAAC,OAAO,CAAC;QACvBC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;QAC/B;MACF,KAAK,GAAG;QACNN,OAAO,GAAG,MAAM;QAChB;MACF,KAAK,GAAG;QACNA,OAAO,GAAG,SAAS;QACnB;MACF,KAAK,GAAG;QACNA,OAAO,GAAG,SAAS;QACnB;MACF;QACEA,OAAO,GAAG,OAAOR,KAAK,CAACI,QAAQ,CAACY,MAAM,EAAE;IAC5C;EACF,CAAC,MAAM,IAAIhB,KAAK,CAACO,IAAI,KAAK,cAAc,EAAE;IACxCC,OAAO,GAAG,MAAM;EAClB;EAEAxB,OAAO,CAAC;IACNwB,OAAO;IACPC,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,OAAOR,OAAO,CAACC,MAAM,CAACH,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAed,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}