# 胜利投资理财网站

基于SpringBoot + Vue的投资理财网站，包含用户注册、登录等基础功能。

## 项目结构

```
胜利投资理财网站/
├── backend/                    # SpringBoot后端
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/com/investment/
│   │   │   │   ├── controller/     # 控制器层
│   │   │   │   ├── service/        # 服务层
│   │   │   │   ├── mapper/         # 数据访问层
│   │   │   │   ├── entity/         # 实体类
│   │   │   │   ├── dto/            # 数据传输对象
│   │   │   │   ├── utils/          # 工具类
│   │   │   │   └── config/         # 配置类
│   │   │   └── resources/
│   │   │       ├── mapper/         # MyBatis映射文件
│   │   │       ├── sql/            # 数据库脚本
│   │   │       └── application.yml # 应用配置
│   │   └── test/                   # 测试代码
│   └── pom.xml                     # Maven配置
├── frontend/                   # Vue前端
│   ├── src/
│   │   ├── views/              # 页面组件
│   │   ├── components/         # 通用组件
│   │   ├── router/             # 路由配置
│   │   ├── api/                # API接口
│   │   ├── utils/              # 工具函数
│   │   └── assets/             # 静态资源
│   ├── public/                 # 公共文件
│   ├── package.json            # 依赖配置
│   └── vue.config.js           # Vue配置
└── README.md                   # 项目说明
```

## 技术栈

### 后端
- **SpringBoot 2.7.14** - 主框架
- **MyBatis Plus 3.5.3.1** - ORM框架
- **MySQL 8.0** - 数据库
- **JWT** - 用户认证
- **Lombok** - 简化代码

### 前端
- **Vue 2.6.14** - 前端框架
- **Vue Router 3.5.1** - 路由管理
- **Element UI 2.15.13** - UI组件库
- **Axios 1.4.0** - HTTP客户端
- **js-cookie 3.0.5** - Cookie管理

## 环境要求

- **JDK 8+**
- **Node.js 14+**
- **MySQL 8.0+**
- **Maven 3.6+**

## 快速开始

### 1. 数据库配置

1. 创建MySQL数据库：
```sql
CREATE DATABASE investment_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 执行初始化脚本：
```bash
mysql -u root -p123456 investment_db < backend/src/main/resources/sql/init.sql
```

### 2. 后端启动

1. 进入后端目录：
```bash
cd backend
```

2. 安装依赖并启动：
```bash
mvn clean install
mvn spring-boot:run
```

后端服务将在 `http://localhost:8080` 启动

### 3. 前端启动

1. 进入前端目录：
```bash
cd frontend
```

2. 安装依赖：
```bash
npm install
```

3. 启动开发服务器：
```bash
npm run serve
```

前端服务将在 `http://localhost:3000` 启动

## 功能特性

### 已实现功能
- ✅ 用户注册
- ✅ 用户登录
- ✅ JWT身份认证
- ✅ 路由守卫
- ✅ 全局异常处理
- ✅ CORS跨域配置
- ✅ 响应式布局

### 待开发功能
- ⏳ 用户个人中心
- ⏳ 投资产品管理
- ⏳ 理财计算器
- ⏳ 交易记录
- ⏳ 资金管理
- ⏳ 数据统计

## API接口

### 用户相关
- `POST /api/user/register` - 用户注册
- `POST /api/user/login` - 用户登录
- `GET /api/user/check-username` - 检查用户名
- `GET /api/user/test` - 测试接口

## 默认账户

系统已预置测试账户：

| 用户名 | 密码 | 角色 |
|--------|------|------|
| admin | 123456 | 管理员 |
| testuser | 123456 | 普通用户 |

## 开发说明

### 后端开发
- 使用MyBatis Plus简化数据库操作
- JWT token有效期为24小时
- 密码使用BCrypt加密
- 统一返回Result格式

### 前端开发
- 使用Element UI组件库
- Axios自动处理token和错误
- 路由守卫自动检查登录状态
- 响应式设计适配移动端

## 部署说明

### 后端部署
```bash
mvn clean package
java -jar target/investment-backend-1.0.0.jar
```

### 前端部署
```bash
npm run build
# 将dist目录部署到Web服务器
```

## 注意事项

1. 确保MySQL服务正常运行
2. 修改`application.yml`中的数据库连接信息
3. 生产环境请修改JWT密钥
4. 建议使用HTTPS协议

## 联系方式

如有问题，请联系开发团队。
