<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.investment.mapper.UserMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.investment.entity.User">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="real_name" property="realName" jdbcType="VARCHAR"/>
        <result column="id_card" property="idCard" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="balance" property="balance" jdbcType="DECIMAL"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, username, password, email, phone, real_name, id_card, status, balance, 
        create_time, update_time, deleted
    </sql>

    <!-- 根据用户名查询用户 -->
    <select id="findByUsername" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM users
        WHERE username = #{username} AND deleted = 0
    </select>

    <!-- 根据邮箱查询用户 -->
    <select id="findByEmail" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM users
        WHERE email = #{email} AND deleted = 0
    </select>

    <!-- 根据手机号查询用户 -->
    <select id="findByPhone" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM users
        WHERE phone = #{phone} AND deleted = 0
    </select>

    <!-- 检查用户名是否存在 -->
    <select id="existsByUsername" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM users
        WHERE username = #{username} AND deleted = 0
    </select>

    <!-- 检查邮箱是否存在 -->
    <select id="existsByEmail" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM users
        WHERE email = #{email} AND deleted = 0
    </select>

    <!-- 检查手机号是否存在 -->
    <select id="existsByPhone" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM users
        WHERE phone = #{phone} AND deleted = 0
    </select>

</mapper>
