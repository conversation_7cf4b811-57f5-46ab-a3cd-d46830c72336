import request from './request'

/**
 * 用户登录
 * @param {Object} data 登录数据
 * @returns {Promise}
 */
export function login(data) {
  return request({
    url: '/user/login',
    method: 'post',
    data
  })
}

/**
 * 用户注册
 * @param {Object} data 注册数据
 * @returns {Promise}
 */
export function register(data) {
  return request({
    url: '/user/register',
    method: 'post',
    data
  })
}

/**
 * 检查用户名是否可用
 * @param {String} username 用户名
 * @returns {Promise}
 */
export function checkUsername(username) {
  return request({
    url: '/user/check-username',
    method: 'get',
    params: { username }
  })
}

/**
 * 测试接口
 * @returns {Promise}
 */
export function test() {
  return request({
    url: '/user/test',
    method: 'get'
  })
}
