{"ast": null, "code": "import Vue from 'vue';\nimport App from './App.vue';\nimport router from './router';\nimport ElementUI from 'element-ui';\nimport 'element-ui/lib/theme-chalk/index.css';\nimport './assets/css/global.css';\nVue.config.productionTip = false;\nVue.use(ElementUI);\nnew Vue({\n  router,\n  render: h => h(App)\n}).$mount('#app');", "map": {"version": 3, "names": ["<PERSON><PERSON>", "App", "router", "ElementUI", "config", "productionTip", "use", "render", "h", "$mount"], "sources": ["E:/java毕设/胜利投资理财网站/frontend/src/main.js"], "sourcesContent": ["import Vue from 'vue'\nimport App from './App.vue'\nimport router from './router'\nimport ElementUI from 'element-ui'\nimport 'element-ui/lib/theme-chalk/index.css'\nimport './assets/css/global.css'\n\nVue.config.productionTip = false\n\nVue.use(ElementUI)\n\nnew Vue({\n  router,\n  render: h => h(App)\n}).$mount('#app')\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,sCAAsC;AAC7C,OAAO,yBAAyB;AAEhCH,GAAG,CAACI,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhCL,GAAG,CAACM,GAAG,CAACH,SAAS,CAAC;AAElB,IAAIH,GAAG,CAAC;EACNE,MAAM;EACNK,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACP,GAAG;AACpB,CAAC,CAAC,CAACQ,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}