{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { login } from '@/api/user';\nimport Cookies from 'js-cookie';\nexport default {\n  name: 'Login',\n  data() {\n    return {\n      loginForm: {\n        username: '',\n        password: ''\n      },\n      loginRules: {\n        username: [{\n          required: true,\n          message: '请输入用户名',\n          trigger: 'blur'\n        }, {\n          min: 3,\n          max: 20,\n          message: '用户名长度在 3 到 20 个字符',\n          trigger: 'blur'\n        }],\n        password: [{\n          required: true,\n          message: '请输入密码',\n          trigger: 'blur'\n        }, {\n          min: 6,\n          max: 20,\n          message: '密码长度在 6 到 20 个字符',\n          trigger: 'blur'\n        }]\n      },\n      loading: false\n    };\n  },\n  methods: {\n    handleLogin() {\n      this.$refs.loginForm.validate(async valid => {\n        if (!valid) {\n          return false;\n        }\n        this.loading = true;\n        try {\n          const response = await login(this.loginForm);\n          if (response.code === 200) {\n            // 保存token\n            Cookies.set('token', response.data, {\n              expires: 1\n            });\n            this.$message.success('登录成功');\n\n            // 跳转到首页或之前访问的页面\n            const redirect = this.$route.query.redirect || '/dashboard';\n            this.$router.push(redirect);\n          } else {\n            this.$message.error(response.message || '登录失败');\n          }\n        } catch (error) {\n          console.error('登录错误：', error);\n          this.$message.error('登录失败，请检查网络连接');\n        } finally {\n          this.loading = false;\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["login", "Cookies", "name", "data", "loginForm", "username", "password", "loginRules", "required", "message", "trigger", "min", "max", "loading", "methods", "handleLogin", "$refs", "validate", "valid", "response", "code", "set", "expires", "$message", "success", "redirect", "$route", "query", "$router", "push", "error", "console"], "sources": ["src/views/Login.vue"], "sourcesContent": ["<template>\n  <div class=\"auth-container\">\n    <div class=\"auth-card\">\n      <h2 class=\"auth-title\">用户登录</h2>\n      <el-form\n        ref=\"loginForm\"\n        :model=\"loginForm\"\n        :rules=\"loginRules\"\n        class=\"auth-form\"\n        @submit.native.prevent=\"handleLogin\"\n      >\n        <el-form-item prop=\"username\">\n          <el-input\n            v-model=\"loginForm.username\"\n            placeholder=\"请输入用户名\"\n            prefix-icon=\"el-icon-user\"\n            clearable\n          />\n        </el-form-item>\n        \n        <el-form-item prop=\"password\">\n          <el-input\n            v-model=\"loginForm.password\"\n            type=\"password\"\n            placeholder=\"请输入密码\"\n            prefix-icon=\"el-icon-lock\"\n            show-password\n            clearable\n            @keyup.enter.native=\"handleLogin\"\n          />\n        </el-form-item>\n        \n        <el-form-item>\n          <el-button\n            type=\"primary\"\n            class=\"auth-button\"\n            :loading=\"loading\"\n            @click=\"handleLogin\"\n          >\n            {{ loading ? '登录中...' : '登录' }}\n          </el-button>\n        </el-form-item>\n      </el-form>\n      \n      <div class=\"auth-link\">\n        <span>还没有账号？</span>\n        <router-link to=\"/register\">立即注册</router-link>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { login } from '@/api/user'\nimport Cookies from 'js-cookie'\n\nexport default {\n  name: 'Login',\n  data() {\n    return {\n      loginForm: {\n        username: '',\n        password: ''\n      },\n      loginRules: {\n        username: [\n          { required: true, message: '请输入用户名', trigger: 'blur' },\n          { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }\n        ],\n        password: [\n          { required: true, message: '请输入密码', trigger: 'blur' },\n          { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }\n        ]\n      },\n      loading: false\n    }\n  },\n  methods: {\n    handleLogin() {\n      this.$refs.loginForm.validate(async (valid) => {\n        if (!valid) {\n          return false\n        }\n        \n        this.loading = true\n        try {\n          const response = await login(this.loginForm)\n          if (response.code === 200) {\n            // 保存token\n            Cookies.set('token', response.data, { expires: 1 })\n            \n            this.$message.success('登录成功')\n            \n            // 跳转到首页或之前访问的页面\n            const redirect = this.$route.query.redirect || '/dashboard'\n            this.$router.push(redirect)\n          } else {\n            this.$message.error(response.message || '登录失败')\n          }\n        } catch (error) {\n          console.error('登录错误：', error)\n          this.$message.error('登录失败，请检查网络连接')\n        } finally {\n          this.loading = false\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* 组件特定样式可以在这里添加 */\n</style>\n"], "mappings": ";AAqDA,SAAAA,KAAA;AACA,OAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;QACAC,QAAA;QACAC,QAAA;MACA;MACAC,UAAA;QACAF,QAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAJ,QAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAG,OAAA;IACA;EACA;EACAC,OAAA;IACAC,YAAA;MACA,KAAAC,KAAA,CAAAZ,SAAA,CAAAa,QAAA,OAAAC,KAAA;QACA,KAAAA,KAAA;UACA;QACA;QAEA,KAAAL,OAAA;QACA;UACA,MAAAM,QAAA,SAAAnB,KAAA,MAAAI,SAAA;UACA,IAAAe,QAAA,CAAAC,IAAA;YACA;YACAnB,OAAA,CAAAoB,GAAA,UAAAF,QAAA,CAAAhB,IAAA;cAAAmB,OAAA;YAAA;YAEA,KAAAC,QAAA,CAAAC,OAAA;;YAEA;YACA,MAAAC,QAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAF,QAAA;YACA,KAAAG,OAAA,CAAAC,IAAA,CAAAJ,QAAA;UACA;YACA,KAAAF,QAAA,CAAAO,KAAA,CAAAX,QAAA,CAAAV,OAAA;UACA;QACA,SAAAqB,KAAA;UACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;UACA,KAAAP,QAAA,CAAAO,KAAA;QACA;UACA,KAAAjB,OAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}