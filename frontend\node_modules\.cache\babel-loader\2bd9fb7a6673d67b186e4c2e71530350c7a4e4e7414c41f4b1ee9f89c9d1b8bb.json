{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"auth-container\"\n  }, [_c(\"div\", {\n    staticClass: \"auth-card\"\n  }, [_c(\"h2\", {\n    staticClass: \"auth-title\"\n  }, [_vm._v(\"用户注册\")]), _c(\"el-form\", {\n    ref: \"registerForm\",\n    staticClass: \"auth-form\",\n    attrs: {\n      model: _vm.registerForm,\n      rules: _vm.registerRules\n    },\n    nativeOn: {\n      submit: function ($event) {\n        $event.preventDefault();\n        return _vm.handleRegister.apply(null, arguments);\n      }\n    }\n  }, [_c(\"el-form-item\", {\n    attrs: {\n      prop: \"username\"\n    }\n  }, [_c(\"el-input\", {\n    attrs: {\n      placeholder: \"请输入用户名\",\n      \"prefix-icon\": \"el-icon-user\",\n      clearable: \"\"\n    },\n    model: {\n      value: _vm.registerForm.username,\n      callback: function ($$v) {\n        _vm.$set(_vm.registerForm, \"username\", $$v);\n      },\n      expression: \"registerForm.username\"\n    }\n  })], 1), _c(\"el-form-item\", {\n    attrs: {\n      prop: \"email\"\n    }\n  }, [_c(\"el-input\", {\n    attrs: {\n      placeholder: \"请输入邮箱\",\n      \"prefix-icon\": \"el-icon-message\",\n      clearable: \"\"\n    },\n    model: {\n      value: _vm.registerForm.email,\n      callback: function ($$v) {\n        _vm.$set(_vm.registerForm, \"email\", $$v);\n      },\n      expression: \"registerForm.email\"\n    }\n  })], 1), _c(\"el-form-item\", {\n    attrs: {\n      prop: \"phone\"\n    }\n  }, [_c(\"el-input\", {\n    attrs: {\n      placeholder: \"请输入手机号\",\n      \"prefix-icon\": \"el-icon-phone\",\n      clearable: \"\"\n    },\n    model: {\n      value: _vm.registerForm.phone,\n      callback: function ($$v) {\n        _vm.$set(_vm.registerForm, \"phone\", $$v);\n      },\n      expression: \"registerForm.phone\"\n    }\n  })], 1), _c(\"el-form-item\", {\n    attrs: {\n      prop: \"realName\"\n    }\n  }, [_c(\"el-input\", {\n    attrs: {\n      placeholder: \"请输入真实姓名（可选）\",\n      \"prefix-icon\": \"el-icon-user-solid\",\n      clearable: \"\"\n    },\n    model: {\n      value: _vm.registerForm.realName,\n      callback: function ($$v) {\n        _vm.$set(_vm.registerForm, \"realName\", $$v);\n      },\n      expression: \"registerForm.realName\"\n    }\n  })], 1), _c(\"el-form-item\", {\n    attrs: {\n      prop: \"password\"\n    }\n  }, [_c(\"el-input\", {\n    attrs: {\n      type: \"password\",\n      placeholder: \"请输入密码\",\n      \"prefix-icon\": \"el-icon-lock\",\n      \"show-password\": \"\",\n      clearable: \"\"\n    },\n    model: {\n      value: _vm.registerForm.password,\n      callback: function ($$v) {\n        _vm.$set(_vm.registerForm, \"password\", $$v);\n      },\n      expression: \"registerForm.password\"\n    }\n  })], 1), _c(\"el-form-item\", {\n    attrs: {\n      prop: \"confirmPassword\"\n    }\n  }, [_c(\"el-input\", {\n    attrs: {\n      type: \"password\",\n      placeholder: \"请确认密码\",\n      \"prefix-icon\": \"el-icon-lock\",\n      \"show-password\": \"\",\n      clearable: \"\"\n    },\n    nativeOn: {\n      keyup: function ($event) {\n        if (!$event.type.indexOf(\"key\") && _vm._k($event.keyCode, \"enter\", 13, $event.key, \"Enter\")) return null;\n        return _vm.handleRegister.apply(null, arguments);\n      }\n    },\n    model: {\n      value: _vm.registerForm.confirmPassword,\n      callback: function ($$v) {\n        _vm.$set(_vm.registerForm, \"confirmPassword\", $$v);\n      },\n      expression: \"registerForm.confirmPassword\"\n    }\n  })], 1), _c(\"el-form-item\", [_c(\"el-button\", {\n    staticClass: \"auth-button\",\n    attrs: {\n      type: \"primary\",\n      loading: _vm.loading\n    },\n    on: {\n      click: _vm.handleRegister\n    }\n  }, [_vm._v(\" \" + _vm._s(_vm.loading ? \"注册中...\" : \"注册\") + \" \")])], 1)], 1), _c(\"div\", {\n    staticClass: \"auth-link\"\n  }, [_c(\"span\", [_vm._v(\"已有账号？\")]), _c(\"router-link\", {\n    attrs: {\n      to: \"/login\"\n    }\n  }, [_vm._v(\"立即登录\")])], 1)], 1)]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "ref", "attrs", "model", "registerForm", "rules", "registerRules", "nativeOn", "submit", "$event", "preventDefault", "handleRegister", "apply", "arguments", "prop", "placeholder", "clearable", "value", "username", "callback", "$$v", "$set", "expression", "email", "phone", "realName", "type", "password", "keyup", "indexOf", "_k", "keyCode", "key", "confirmPassword", "loading", "on", "click", "_s", "to", "staticRenderFns", "_withStripped"], "sources": ["E:/java毕设/胜利投资理财网站/frontend/src/views/Register.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"auth-container\" }, [\n    _c(\n      \"div\",\n      { staticClass: \"auth-card\" },\n      [\n        _c(\"h2\", { staticClass: \"auth-title\" }, [_vm._v(\"用户注册\")]),\n        _c(\n          \"el-form\",\n          {\n            ref: \"registerForm\",\n            staticClass: \"auth-form\",\n            attrs: { model: _vm.registerForm, rules: _vm.registerRules },\n            nativeOn: {\n              submit: function ($event) {\n                $event.preventDefault()\n                return _vm.handleRegister.apply(null, arguments)\n              },\n            },\n          },\n          [\n            _c(\n              \"el-form-item\",\n              { attrs: { prop: \"username\" } },\n              [\n                _c(\"el-input\", {\n                  attrs: {\n                    placeholder: \"请输入用户名\",\n                    \"prefix-icon\": \"el-icon-user\",\n                    clearable: \"\",\n                  },\n                  model: {\n                    value: _vm.registerForm.username,\n                    callback: function ($$v) {\n                      _vm.$set(_vm.registerForm, \"username\", $$v)\n                    },\n                    expression: \"registerForm.username\",\n                  },\n                }),\n              ],\n              1\n            ),\n            _c(\n              \"el-form-item\",\n              { attrs: { prop: \"email\" } },\n              [\n                _c(\"el-input\", {\n                  attrs: {\n                    placeholder: \"请输入邮箱\",\n                    \"prefix-icon\": \"el-icon-message\",\n                    clearable: \"\",\n                  },\n                  model: {\n                    value: _vm.registerForm.email,\n                    callback: function ($$v) {\n                      _vm.$set(_vm.registerForm, \"email\", $$v)\n                    },\n                    expression: \"registerForm.email\",\n                  },\n                }),\n              ],\n              1\n            ),\n            _c(\n              \"el-form-item\",\n              { attrs: { prop: \"phone\" } },\n              [\n                _c(\"el-input\", {\n                  attrs: {\n                    placeholder: \"请输入手机号\",\n                    \"prefix-icon\": \"el-icon-phone\",\n                    clearable: \"\",\n                  },\n                  model: {\n                    value: _vm.registerForm.phone,\n                    callback: function ($$v) {\n                      _vm.$set(_vm.registerForm, \"phone\", $$v)\n                    },\n                    expression: \"registerForm.phone\",\n                  },\n                }),\n              ],\n              1\n            ),\n            _c(\n              \"el-form-item\",\n              { attrs: { prop: \"realName\" } },\n              [\n                _c(\"el-input\", {\n                  attrs: {\n                    placeholder: \"请输入真实姓名（可选）\",\n                    \"prefix-icon\": \"el-icon-user-solid\",\n                    clearable: \"\",\n                  },\n                  model: {\n                    value: _vm.registerForm.realName,\n                    callback: function ($$v) {\n                      _vm.$set(_vm.registerForm, \"realName\", $$v)\n                    },\n                    expression: \"registerForm.realName\",\n                  },\n                }),\n              ],\n              1\n            ),\n            _c(\n              \"el-form-item\",\n              { attrs: { prop: \"password\" } },\n              [\n                _c(\"el-input\", {\n                  attrs: {\n                    type: \"password\",\n                    placeholder: \"请输入密码\",\n                    \"prefix-icon\": \"el-icon-lock\",\n                    \"show-password\": \"\",\n                    clearable: \"\",\n                  },\n                  model: {\n                    value: _vm.registerForm.password,\n                    callback: function ($$v) {\n                      _vm.$set(_vm.registerForm, \"password\", $$v)\n                    },\n                    expression: \"registerForm.password\",\n                  },\n                }),\n              ],\n              1\n            ),\n            _c(\n              \"el-form-item\",\n              { attrs: { prop: \"confirmPassword\" } },\n              [\n                _c(\"el-input\", {\n                  attrs: {\n                    type: \"password\",\n                    placeholder: \"请确认密码\",\n                    \"prefix-icon\": \"el-icon-lock\",\n                    \"show-password\": \"\",\n                    clearable: \"\",\n                  },\n                  nativeOn: {\n                    keyup: function ($event) {\n                      if (\n                        !$event.type.indexOf(\"key\") &&\n                        _vm._k($event.keyCode, \"enter\", 13, $event.key, \"Enter\")\n                      )\n                        return null\n                      return _vm.handleRegister.apply(null, arguments)\n                    },\n                  },\n                  model: {\n                    value: _vm.registerForm.confirmPassword,\n                    callback: function ($$v) {\n                      _vm.$set(_vm.registerForm, \"confirmPassword\", $$v)\n                    },\n                    expression: \"registerForm.confirmPassword\",\n                  },\n                }),\n              ],\n              1\n            ),\n            _c(\n              \"el-form-item\",\n              [\n                _c(\n                  \"el-button\",\n                  {\n                    staticClass: \"auth-button\",\n                    attrs: { type: \"primary\", loading: _vm.loading },\n                    on: { click: _vm.handleRegister },\n                  },\n                  [\n                    _vm._v(\n                      \" \" + _vm._s(_vm.loading ? \"注册中...\" : \"注册\") + \" \"\n                    ),\n                  ]\n                ),\n              ],\n              1\n            ),\n          ],\n          1\n        ),\n        _c(\n          \"div\",\n          { staticClass: \"auth-link\" },\n          [\n            _c(\"span\", [_vm._v(\"已有账号？\")]),\n            _c(\"router-link\", { attrs: { to: \"/login\" } }, [\n              _vm._v(\"立即登录\"),\n            ]),\n          ],\n          1\n        ),\n      ],\n      1\n    ),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAClDF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5B,CACEF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAACH,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EACzDH,EAAE,CACA,SAAS,EACT;IACEI,GAAG,EAAE,cAAc;IACnBF,WAAW,EAAE,WAAW;IACxBG,KAAK,EAAE;MAAEC,KAAK,EAAEP,GAAG,CAACQ,YAAY;MAAEC,KAAK,EAAET,GAAG,CAACU;IAAc,CAAC;IAC5DC,QAAQ,EAAE;MACRC,MAAM,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACxBA,MAAM,CAACC,cAAc,CAAC,CAAC;QACvB,OAAOd,GAAG,CAACe,cAAc,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAClD;IACF;EACF,CAAC,EACD,CACEhB,EAAE,CACA,cAAc,EACd;IAAEK,KAAK,EAAE;MAAEY,IAAI,EAAE;IAAW;EAAE,CAAC,EAC/B,CACEjB,EAAE,CAAC,UAAU,EAAE;IACbK,KAAK,EAAE;MACLa,WAAW,EAAE,QAAQ;MACrB,aAAa,EAAE,cAAc;MAC7BC,SAAS,EAAE;IACb,CAAC;IACDb,KAAK,EAAE;MACLc,KAAK,EAAErB,GAAG,CAACQ,YAAY,CAACc,QAAQ;MAChCC,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvBxB,GAAG,CAACyB,IAAI,CAACzB,GAAG,CAACQ,YAAY,EAAE,UAAU,EAAEgB,GAAG,CAAC;MAC7C,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDzB,EAAE,CACA,cAAc,EACd;IAAEK,KAAK,EAAE;MAAEY,IAAI,EAAE;IAAQ;EAAE,CAAC,EAC5B,CACEjB,EAAE,CAAC,UAAU,EAAE;IACbK,KAAK,EAAE;MACLa,WAAW,EAAE,OAAO;MACpB,aAAa,EAAE,iBAAiB;MAChCC,SAAS,EAAE;IACb,CAAC;IACDb,KAAK,EAAE;MACLc,KAAK,EAAErB,GAAG,CAACQ,YAAY,CAACmB,KAAK;MAC7BJ,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvBxB,GAAG,CAACyB,IAAI,CAACzB,GAAG,CAACQ,YAAY,EAAE,OAAO,EAAEgB,GAAG,CAAC;MAC1C,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDzB,EAAE,CACA,cAAc,EACd;IAAEK,KAAK,EAAE;MAAEY,IAAI,EAAE;IAAQ;EAAE,CAAC,EAC5B,CACEjB,EAAE,CAAC,UAAU,EAAE;IACbK,KAAK,EAAE;MACLa,WAAW,EAAE,QAAQ;MACrB,aAAa,EAAE,eAAe;MAC9BC,SAAS,EAAE;IACb,CAAC;IACDb,KAAK,EAAE;MACLc,KAAK,EAAErB,GAAG,CAACQ,YAAY,CAACoB,KAAK;MAC7BL,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvBxB,GAAG,CAACyB,IAAI,CAACzB,GAAG,CAACQ,YAAY,EAAE,OAAO,EAAEgB,GAAG,CAAC;MAC1C,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDzB,EAAE,CACA,cAAc,EACd;IAAEK,KAAK,EAAE;MAAEY,IAAI,EAAE;IAAW;EAAE,CAAC,EAC/B,CACEjB,EAAE,CAAC,UAAU,EAAE;IACbK,KAAK,EAAE;MACLa,WAAW,EAAE,aAAa;MAC1B,aAAa,EAAE,oBAAoB;MACnCC,SAAS,EAAE;IACb,CAAC;IACDb,KAAK,EAAE;MACLc,KAAK,EAAErB,GAAG,CAACQ,YAAY,CAACqB,QAAQ;MAChCN,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvBxB,GAAG,CAACyB,IAAI,CAACzB,GAAG,CAACQ,YAAY,EAAE,UAAU,EAAEgB,GAAG,CAAC;MAC7C,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDzB,EAAE,CACA,cAAc,EACd;IAAEK,KAAK,EAAE;MAAEY,IAAI,EAAE;IAAW;EAAE,CAAC,EAC/B,CACEjB,EAAE,CAAC,UAAU,EAAE;IACbK,KAAK,EAAE;MACLwB,IAAI,EAAE,UAAU;MAChBX,WAAW,EAAE,OAAO;MACpB,aAAa,EAAE,cAAc;MAC7B,eAAe,EAAE,EAAE;MACnBC,SAAS,EAAE;IACb,CAAC;IACDb,KAAK,EAAE;MACLc,KAAK,EAAErB,GAAG,CAACQ,YAAY,CAACuB,QAAQ;MAChCR,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvBxB,GAAG,CAACyB,IAAI,CAACzB,GAAG,CAACQ,YAAY,EAAE,UAAU,EAAEgB,GAAG,CAAC;MAC7C,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDzB,EAAE,CACA,cAAc,EACd;IAAEK,KAAK,EAAE;MAAEY,IAAI,EAAE;IAAkB;EAAE,CAAC,EACtC,CACEjB,EAAE,CAAC,UAAU,EAAE;IACbK,KAAK,EAAE;MACLwB,IAAI,EAAE,UAAU;MAChBX,WAAW,EAAE,OAAO;MACpB,aAAa,EAAE,cAAc;MAC7B,eAAe,EAAE,EAAE;MACnBC,SAAS,EAAE;IACb,CAAC;IACDT,QAAQ,EAAE;MACRqB,KAAK,EAAE,SAAAA,CAAUnB,MAAM,EAAE;QACvB,IACE,CAACA,MAAM,CAACiB,IAAI,CAACG,OAAO,CAAC,KAAK,CAAC,IAC3BjC,GAAG,CAACkC,EAAE,CAACrB,MAAM,CAACsB,OAAO,EAAE,OAAO,EAAE,EAAE,EAAEtB,MAAM,CAACuB,GAAG,EAAE,OAAO,CAAC,EAExD,OAAO,IAAI;QACb,OAAOpC,GAAG,CAACe,cAAc,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAClD;IACF,CAAC;IACDV,KAAK,EAAE;MACLc,KAAK,EAAErB,GAAG,CAACQ,YAAY,CAAC6B,eAAe;MACvCd,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvBxB,GAAG,CAACyB,IAAI,CAACzB,GAAG,CAACQ,YAAY,EAAE,iBAAiB,EAAEgB,GAAG,CAAC;MACpD,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDzB,EAAE,CACA,cAAc,EACd,CACEA,EAAE,CACA,WAAW,EACX;IACEE,WAAW,EAAE,aAAa;IAC1BG,KAAK,EAAE;MAAEwB,IAAI,EAAE,SAAS;MAAEQ,OAAO,EAAEtC,GAAG,CAACsC;IAAQ,CAAC;IAChDC,EAAE,EAAE;MAAEC,KAAK,EAAExC,GAAG,CAACe;IAAe;EAClC,CAAC,EACD,CACEf,GAAG,CAACI,EAAE,CACJ,GAAG,GAAGJ,GAAG,CAACyC,EAAE,CAACzC,GAAG,CAACsC,OAAO,GAAG,QAAQ,GAAG,IAAI,CAAC,GAAG,GAChD,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDrC,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5B,CACEF,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAC7BH,EAAE,CAAC,aAAa,EAAE;IAAEK,KAAK,EAAE;MAAEoC,EAAE,EAAE;IAAS;EAAE,CAAC,EAAE,CAC7C1C,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC;AACJ,CAAC;AACD,IAAIuC,eAAe,GAAG,EAAE;AACxB5C,MAAM,CAAC6C,aAAa,GAAG,IAAI;AAE3B,SAAS7C,MAAM,EAAE4C,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}