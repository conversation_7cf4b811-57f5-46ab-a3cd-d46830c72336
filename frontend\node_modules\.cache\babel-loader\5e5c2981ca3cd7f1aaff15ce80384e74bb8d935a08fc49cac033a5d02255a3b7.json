{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { test } from '@/api/user';\nimport Cookies from 'js-cookie';\nexport default {\n  name: 'Dashboard',\n  data() {\n    return {\n      testing: false,\n      testResult: null\n    };\n  },\n  methods: {\n    handleCommand(command) {\n      switch (command) {\n        case 'profile':\n          this.$message.info('个人中心功能待开发');\n          break;\n        case 'logout':\n          this.logout();\n          break;\n      }\n    },\n    logout() {\n      this.$confirm('确定要退出登录吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        Cookies.remove('token');\n        this.$message.success('退出登录成功');\n        this.$router.push('/login');\n      });\n    },\n    async testBackend() {\n      this.testing = true;\n      this.testResult = null;\n      try {\n        const response = await test();\n        this.testResult = {\n          success: true,\n          message: response.data || '后端连接成功'\n        };\n      } catch (error) {\n        this.testResult = {\n          success: false,\n          message: '后端连接失败：' + error.message\n        };\n      } finally {\n        this.testing = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["test", "Cookies", "name", "data", "testing", "testResult", "methods", "handleCommand", "command", "$message", "info", "logout", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "remove", "success", "$router", "push", "testBackend", "response", "message", "error"], "sources": ["src/views/Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard\">\n    <el-container>\n      <!-- 头部 -->\n      <el-header class=\"header\">\n        <div class=\"header-left\">\n          <h2>胜利投资理财网站</h2>\n        </div>\n        <div class=\"header-right\">\n          <el-dropdown @command=\"handleCommand\">\n            <span class=\"el-dropdown-link\">\n              欢迎您 <i class=\"el-icon-arrow-down el-icon--right\"></i>\n            </span>\n            <el-dropdown-menu slot=\"dropdown\">\n              <el-dropdown-item command=\"profile\">个人中心</el-dropdown-item>\n              <el-dropdown-item command=\"logout\">退出登录</el-dropdown-item>\n            </el-dropdown-menu>\n          </el-dropdown>\n        </div>\n      </el-header>\n      \n      <!-- 主体内容 -->\n      <el-main class=\"main\">\n        <div class=\"welcome-card\">\n          <el-card>\n            <h3>欢迎来到胜利投资理财网站</h3>\n            <p>您已成功登录系统！</p>\n            <p>这里是用户控制台，后续可以添加更多功能模块。</p>\n            \n            <div class=\"test-section\">\n              <h4>测试后端连接：</h4>\n              <el-button type=\"primary\" @click=\"testBackend\" :loading=\"testing\">\n                测试后端接口\n              </el-button>\n              <p v-if=\"testResult\" :class=\"testResult.success ? 'success' : 'error'\">\n                {{ testResult.message }}\n              </p>\n            </div>\n          </el-card>\n        </div>\n      </el-main>\n    </el-container>\n  </div>\n</template>\n\n<script>\nimport { test } from '@/api/user'\nimport Cookies from 'js-cookie'\n\nexport default {\n  name: 'Dashboard',\n  data() {\n    return {\n      testing: false,\n      testResult: null\n    }\n  },\n  methods: {\n    handleCommand(command) {\n      switch (command) {\n        case 'profile':\n          this.$message.info('个人中心功能待开发')\n          break\n        case 'logout':\n          this.logout()\n          break\n      }\n    },\n    \n    logout() {\n      this.$confirm('确定要退出登录吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        Cookies.remove('token')\n        this.$message.success('退出登录成功')\n        this.$router.push('/login')\n      })\n    },\n    \n    async testBackend() {\n      this.testing = true\n      this.testResult = null\n      \n      try {\n        const response = await test()\n        this.testResult = {\n          success: true,\n          message: response.data || '后端连接成功'\n        }\n      } catch (error) {\n        this.testResult = {\n          success: false,\n          message: '后端连接失败：' + error.message\n        }\n      } finally {\n        this.testing = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.dashboard {\n  min-height: 100vh;\n}\n\n.header {\n  background-color: #409eff;\n  color: white;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 20px;\n}\n\n.header-left h2 {\n  margin: 0;\n}\n\n.header-right {\n  cursor: pointer;\n}\n\n.el-dropdown-link {\n  color: white;\n}\n\n.main {\n  padding: 20px;\n  background-color: #f5f5f5;\n}\n\n.welcome-card {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.test-section {\n  margin-top: 30px;\n  padding-top: 20px;\n  border-top: 1px solid #eee;\n}\n\n.test-section h4 {\n  margin-bottom: 15px;\n}\n\n.success {\n  color: #67c23a;\n  margin-top: 10px;\n}\n\n.error {\n  color: #f56c6c;\n  margin-top: 10px;\n}\n</style>\n"], "mappings": ";AA8CA,SAAAA,IAAA;AACA,OAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;IACA;EACA;EACAC,OAAA;IACAC,cAAAC,OAAA;MACA,QAAAA,OAAA;QACA;UACA,KAAAC,QAAA,CAAAC,IAAA;UACA;QACA;UACA,KAAAC,MAAA;UACA;MACA;IACA;IAEAA,OAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACAf,OAAA,CAAAgB,MAAA;QACA,KAAAR,QAAA,CAAAS,OAAA;QACA,KAAAC,OAAA,CAAAC,IAAA;MACA;IACA;IAEA,MAAAC,YAAA;MACA,KAAAjB,OAAA;MACA,KAAAC,UAAA;MAEA;QACA,MAAAiB,QAAA,SAAAtB,IAAA;QACA,KAAAK,UAAA;UACAa,OAAA;UACAK,OAAA,EAAAD,QAAA,CAAAnB,IAAA;QACA;MACA,SAAAqB,KAAA;QACA,KAAAnB,UAAA;UACAa,OAAA;UACAK,OAAA,cAAAC,KAAA,CAAAD;QACA;MACA;QACA,KAAAnB,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}