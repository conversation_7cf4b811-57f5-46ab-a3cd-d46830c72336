{"ast": null, "code": "import request from './request';\n\n/**\n * 用户登录\n * @param {Object} data 登录数据\n * @returns {Promise}\n */\nexport function login(data) {\n  return request({\n    url: '/user/login',\n    method: 'post',\n    data\n  });\n}\n\n/**\n * 用户注册\n * @param {Object} data 注册数据\n * @returns {Promise}\n */\nexport function register(data) {\n  return request({\n    url: '/user/register',\n    method: 'post',\n    data\n  });\n}\n\n/**\n * 检查用户名是否可用\n * @param {String} username 用户名\n * @returns {Promise}\n */\nexport function checkUsername(username) {\n  return request({\n    url: '/user/check-username',\n    method: 'get',\n    params: {\n      username\n    }\n  });\n}\n\n/**\n * 测试接口\n * @returns {Promise}\n */\nexport function test() {\n  return request({\n    url: '/user/test',\n    method: 'get'\n  });\n}", "map": {"version": 3, "names": ["request", "login", "data", "url", "method", "register", "checkUsername", "username", "params", "test"], "sources": ["E:/java毕设/胜利投资理财网站/frontend/src/api/user.js"], "sourcesContent": ["import request from './request'\n\n/**\n * 用户登录\n * @param {Object} data 登录数据\n * @returns {Promise}\n */\nexport function login(data) {\n  return request({\n    url: '/user/login',\n    method: 'post',\n    data\n  })\n}\n\n/**\n * 用户注册\n * @param {Object} data 注册数据\n * @returns {Promise}\n */\nexport function register(data) {\n  return request({\n    url: '/user/register',\n    method: 'post',\n    data\n  })\n}\n\n/**\n * 检查用户名是否可用\n * @param {String} username 用户名\n * @returns {Promise}\n */\nexport function checkUsername(username) {\n  return request({\n    url: '/user/check-username',\n    method: 'get',\n    params: { username }\n  })\n}\n\n/**\n * 测试接口\n * @returns {Promise}\n */\nexport function test() {\n  return request({\n    url: '/user/test',\n    method: 'get'\n  })\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,WAAW;;AAE/B;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAACC,IAAI,EAAE;EAC1B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAE,MAAM;IACdF;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,QAAQA,CAACH,IAAI,EAAE;EAC7B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,MAAM;IACdF;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,aAAaA,CAACC,QAAQ,EAAE;EACtC,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbI,MAAM,EAAE;MAAED;IAAS;EACrB,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,IAAIA,CAAA,EAAG;EACrB,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}