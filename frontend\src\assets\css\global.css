/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
  background-color: #f5f5f5;
}

/* 登录注册页面样式 */
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.auth-card {
  width: 400px;
  padding: 40px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.auth-title {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
  font-size: 24px;
  font-weight: 500;
}

.auth-form .el-form-item {
  margin-bottom: 20px;
}

.auth-form .el-input__inner {
  height: 45px;
  border-radius: 5px;
}

.auth-button {
  width: 100%;
  height: 45px;
  margin-top: 10px;
  border-radius: 5px;
  font-size: 16px;
}

.auth-link {
  text-align: center;
  margin-top: 20px;
}

.auth-link a {
  color: #409eff;
  text-decoration: none;
}

.auth-link a:hover {
  text-decoration: underline;
}

/* 通用样式 */
.text-center {
  text-align: center;
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}
